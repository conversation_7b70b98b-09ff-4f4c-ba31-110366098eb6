/**
 * Popup script for WebAI extension
 * Handles the main popup interface
 */
import browser from 'webextension-polyfill';

// DOM elements
let serverStatusElement, serverInfoElement, serverUrlElement, serverPortElement;
let startServerBtn, stopServerBtn, copyUrlBtn;
let apiKeyDisplay, apiKeyInput, noKeyMessage;
let generateKeyBtn, toggleKeyVisibilityBtn, copyKeyBtn, revokeKeyBtn, keyExpiresElement;
let defaultModelSelect, autoStartCheckbox, portInput;
let openSettingsBtn, openDocsBtn;
let toastContainer;

// State
let currentApiKey = null;
let serverStatus = { isRunning: false, port: 3000, url: 'http://localhost:3000' };

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  initializeElements();
  attachEventListeners();
  await loadSettings();
  await updateServerStatus();
  await updateApiKeyDisplay();
  
  console.log('WebAI popup initialized');
});

function initializeElements() {
  // Server status elements
  serverStatusElement = document.getElementById('server-status');
  serverInfoElement = document.getElementById('server-info');
  serverUrlElement = document.getElementById('server-url');
  serverPortElement = document.getElementById('server-port');
  
  // Server control buttons
  startServerBtn = document.getElementById('start-server-btn');
  stopServerBtn = document.getElementById('stop-server-btn');
  copyUrlBtn = document.getElementById('copy-url-btn');
  
  // API key elements
  apiKeyDisplay = document.getElementById('api-key-display');
  apiKeyInput = document.getElementById('api-key-input');
  noKeyMessage = document.getElementById('no-key-message');
  generateKeyBtn = document.getElementById('generate-key-btn');
  toggleKeyVisibilityBtn = document.getElementById('toggle-key-visibility');
  copyKeyBtn = document.getElementById('copy-key-btn');
  revokeKeyBtn = document.getElementById('revoke-key-btn');
  keyExpiresElement = document.getElementById('key-expires');
  
  // Settings elements
  defaultModelSelect = document.getElementById('default-model');
  autoStartCheckbox = document.getElementById('auto-start');
  portInput = document.getElementById('port-input');
  
  // Action buttons
  openSettingsBtn = document.getElementById('open-settings-btn');
  openDocsBtn = document.getElementById('open-docs-btn');
  
  // Toast container
  toastContainer = document.getElementById('toast-container');
}

function attachEventListeners() {
  // Server controls
  startServerBtn?.addEventListener('click', startServer);
  stopServerBtn?.addEventListener('click', stopServer);
  copyUrlBtn?.addEventListener('click', copyServerUrl);
  
  // API key controls
  generateKeyBtn?.addEventListener('click', generateApiKey);
  toggleKeyVisibilityBtn?.addEventListener('click', toggleKeyVisibility);
  copyKeyBtn?.addEventListener('click', copyApiKey);
  revokeKeyBtn?.addEventListener('click', revokeApiKey);
  
  // Settings
  defaultModelSelect?.addEventListener('change', saveSettings);
  autoStartCheckbox?.addEventListener('change', saveSettings);
  portInput?.addEventListener('change', saveSettings);
  
  // Action buttons
  openSettingsBtn?.addEventListener('click', openSettings);
  openDocsBtn?.addEventListener('click', openDocumentation);
  
  // Listen for background messages
  browser.runtime.onMessage.addListener(handleBackgroundMessage);
}

async function loadSettings() {
  try {
    const settings = await browser.storage.local.get([
      'defaultModel',
      'autoStart', 
      'serverPort'
    ]);
    
    if (defaultModelSelect && settings.defaultModel) {
      defaultModelSelect.value = settings.defaultModel;
    }
    
    if (autoStartCheckbox) {
      autoStartCheckbox.checked = settings.autoStart || false;
    }
    
    if (portInput && settings.serverPort) {
      portInput.value = settings.serverPort;
    }
  } catch (error) {
    console.error('Failed to load settings:', error);
    showToast('Failed to load settings', 'error');
  }
}

async function saveSettings() {
  try {
    const settings = {
      defaultModel: defaultModelSelect?.value,
      autoStart: autoStartCheckbox?.checked,
      serverPort: parseInt(portInput?.value) || 3000
    };
    
    await browser.storage.local.set(settings);
    showToast('Settings saved', 'success');
  } catch (error) {
    console.error('Failed to save settings:', error);
    showToast('Failed to save settings', 'error');
  }
}

async function updateServerStatus() {
  try {
    const response = await browser.runtime.sendMessage({ type: 'GET_SERVER_STATUS' });
    
    if (response.success) {
      serverStatus = response.data;
      updateServerUI();
    }
  } catch (error) {
    console.error('Failed to get server status:', error);
    serverStatus.isRunning = false;
    updateServerUI();
  }
}

function updateServerUI() {
  if (!serverStatusElement) return;
  
  const statusDot = serverStatusElement.querySelector('.status-dot');
  const statusText = serverStatusElement.querySelector('.status-text');
  
  if (serverStatus.isRunning) {
    statusDot.className = 'status-dot online';
    statusText.textContent = 'Online';
    
    if (serverInfoElement) {
      serverInfoElement.style.display = 'block';
    }
    if (serverUrlElement) {
      serverUrlElement.textContent = serverStatus.url;
    }
    if (serverPortElement) {
      serverPortElement.textContent = serverStatus.port;
    }
    
    if (startServerBtn) startServerBtn.style.display = 'none';
    if (stopServerBtn) stopServerBtn.style.display = 'inline-block';
  } else {
    statusDot.className = 'status-dot offline';
    statusText.textContent = 'Offline';
    
    if (serverInfoElement) {
      serverInfoElement.style.display = 'none';
    }
    
    if (startServerBtn) startServerBtn.style.display = 'inline-block';
    if (stopServerBtn) stopServerBtn.style.display = 'none';
  }
}

async function startServer() {
  try {
    if (startServerBtn) {
      startServerBtn.disabled = true;
      startServerBtn.textContent = 'Starting...';
    }
    
    const response = await browser.runtime.sendMessage({ type: 'START_SERVER' });
    
    if (response.success) {
      await updateServerStatus();
      showToast('Server started successfully', 'success');
    } else {
      showToast(`Failed to start server: ${response.error}`, 'error');
    }
  } catch (error) {
    console.error('Failed to start server:', error);
    showToast('Failed to start server', 'error');
  } finally {
    if (startServerBtn) {
      startServerBtn.disabled = false;
      startServerBtn.textContent = 'Start Server';
    }
  }
}

async function stopServer() {
  try {
    if (stopServerBtn) {
      stopServerBtn.disabled = true;
      stopServerBtn.textContent = 'Stopping...';
    }
    
    const response = await browser.runtime.sendMessage({ type: 'STOP_SERVER' });
    
    if (response.success) {
      await updateServerStatus();
      showToast('Server stopped', 'info');
    } else {
      showToast(`Failed to stop server: ${response.error}`, 'error');
    }
  } catch (error) {
    console.error('Failed to stop server:', error);
    showToast('Failed to stop server', 'error');
  } finally {
    if (stopServerBtn) {
      stopServerBtn.disabled = false;
      stopServerBtn.textContent = 'Stop Server';
    }
  }
}

function copyServerUrl() {
  if (serverStatus.isRunning && serverStatus.url) {
    copyToClipboard(serverStatus.url);
    showToast('Server URL copied to clipboard', 'success');
  }
}

async function updateApiKeyDisplay() {
  try {
    const response = await browser.runtime.sendMessage({ type: 'GET_API_KEYS' });
    
    if (response.success && response.data.length > 0) {
      // Show the most recent key
      const latestKey = response.data.sort((a, b) => b.createdAt - a.createdAt)[0];
      currentApiKey = latestKey;
      showApiKey(latestKey);
    } else {
      showNoApiKey();
    }
  } catch (error) {
    console.error('Failed to get API keys:', error);
    showNoApiKey();
  }
}

function showApiKey(keyData) {
  if (apiKeyDisplay) apiKeyDisplay.style.display = 'block';
  if (noKeyMessage) noKeyMessage.style.display = 'none';
  
  if (apiKeyInput) {
    apiKeyInput.value = keyData.id;
  }
  
  if (keyExpiresElement) {
    if (keyData.expiresAt) {
      const expireDate = new Date(keyData.expiresAt);
      keyExpiresElement.textContent = `Expires: ${expireDate.toLocaleDateString()} ${expireDate.toLocaleTimeString()}`;
    } else {
      keyExpiresElement.textContent = 'Expires: Never';
    }
  }
}

function showNoApiKey() {
  if (apiKeyDisplay) apiKeyDisplay.style.display = 'none';
  if (noKeyMessage) noKeyMessage.style.display = 'block';
  currentApiKey = null;
}

async function generateApiKey() {
  try {
    if (generateKeyBtn) {
      generateKeyBtn.disabled = true;
      generateKeyBtn.textContent = 'Generating...';
    }
    
    const response = await browser.runtime.sendMessage({ 
      type: 'GENERATE_API_KEY',
      expiryMs: 24 * 60 * 60 * 1000 // 24 hours
    });
    
    if (response.success) {
      currentApiKey = response.data;
      showApiKey(response.data);
      showToast('New API key generated', 'success');
    } else {
      showToast(`Failed to generate API key: ${response.error}`, 'error');
    }
  } catch (error) {
    console.error('Failed to generate API key:', error);
    showToast('Failed to generate API key', 'error');
  } finally {
    if (generateKeyBtn) {
      generateKeyBtn.disabled = false;
      generateKeyBtn.textContent = 'Generate New';
    }
  }
}

function toggleKeyVisibility() {
  if (!apiKeyInput) return;
  
  if (apiKeyInput.type === 'password') {
    apiKeyInput.type = 'text';
    if (toggleKeyVisibilityBtn) toggleKeyVisibilityBtn.textContent = '🙈';
  } else {
    apiKeyInput.type = 'password';
    if (toggleKeyVisibilityBtn) toggleKeyVisibilityBtn.textContent = '👁️';
  }
}

function copyApiKey() {
  if (currentApiKey) {
    copyToClipboard(currentApiKey.id);
    showToast('API key copied to clipboard', 'success');
  }
}

async function revokeApiKey() {
  if (!currentApiKey) return;
  
  try {
    if (revokeKeyBtn) {
      revokeKeyBtn.disabled = true;
      revokeKeyBtn.textContent = 'Revoking...';
    }
    
    const response = await browser.runtime.sendMessage({
      type: 'REVOKE_API_KEY',
      key: currentApiKey.id
    });
    
    if (response.success) {
      showNoApiKey();
      showToast('API key revoked', 'info');
    } else {
      showToast(`Failed to revoke API key: ${response.error}`, 'error');
    }
  } catch (error) {
    console.error('Failed to revoke API key:', error);
    showToast('Failed to revoke API key', 'error');
  } finally {
    if (revokeKeyBtn) {
      revokeKeyBtn.disabled = false;
      revokeKeyBtn.textContent = 'Revoke';
    }
  }
}

function openSettings() {
  browser.runtime.openOptionsPage();
}

function openDocumentation() {
  browser.tabs.create({ 
    url: 'https://github.com/yourusername/webai-extension#readme' 
  });
}

function handleBackgroundMessage(message) {
  switch (message.type) {
    case 'SERVER_STATUS_UPDATE':
      serverStatus = message.data;
      updateServerUI();
      break;
  }
}

// Utility functions
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).catch(err => {
    console.error('Failed to copy to clipboard:', err);
    // Fallback method
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  });
}

function showToast(message, type = 'info') {
  if (!toastContainer) return;
  
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.textContent = message;
  
  toastContainer.appendChild(toast);
  
  // Show toast
  setTimeout(() => toast.classList.add('show'), 100);
  
  // Hide and remove toast
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => {
      if (toast.parentNode) {
        toastContainer.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

console.log('WebAI popup script loaded');