#!/usr/bin/env node

/**
 * Test script to verify all WebAI endpoints are working
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testEndpoints() {
  console.log('🧪 Testing WebAI Endpoints');
  console.log('==========================\n');

  const tests = [
    {
      name: 'Health Check (v1)',
      path: '/v1/health',
      expected: 200
    },
    {
      name: 'Health Check (direct)',
      path: '/health',
      expected: 200
    },
    {
      name: 'List Models (v1)',
      path: '/v1/models',
      expected: 200
    },
    {
      name: 'List Models (direct)',
      path: '/models',
      expected: 200
    },
    {
      name: 'Chat Completions (v1)',
      path: '/v1/chat/completions',
      method: 'POST',
      data: {
        model: 'claude-web',
        messages: [{ role: 'user', content: 'Hello test' }],
        stream: false
      },
      headers: { 'Authorization': 'Bearer test-key' },
      expected: [200, 401, 500] // Could be any of these depending on setup
    },
    {
      name: 'Chat Completions (direct)',
      path: '/chat/completions',
      method: 'POST',
      data: {
        model: 'claude-web',
        messages: [{ role: 'user', content: 'Hello test' }],
        stream: false
      },
      headers: { 'Authorization': 'Bearer test-key' },
      expected: [200, 401, 500] // Could be any of these depending on setup
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      const result = await makeRequest(
        test.path, 
        test.method || 'GET', 
        test.data, 
        test.headers || {}
      );
      
      const expectedStatuses = Array.isArray(test.expected) ? test.expected : [test.expected];
      const success = expectedStatuses.includes(result.status);
      
      console.log(`  ${success ? '✅' : '❌'} ${test.method || 'GET'} ${test.path}`);
      console.log(`  Status: ${result.status}`);
      
      if (typeof result.data === 'object') {
        console.log(`  Response: ${JSON.stringify(result.data).substring(0, 100)}...`);
      } else {
        console.log(`  Response: ${result.data.substring(0, 100)}...`);
      }
      console.log('');
      
    } catch (error) {
      console.log(`  ❌ ${test.method || 'GET'} ${test.path}`);
      console.log(`  Error: ${error.message}`);
      console.log('');
    }
  }

  console.log('🎯 Cline Configuration:');
  console.log('=====================');
  console.log('Provider: OpenAI Compatible');
  console.log('Base URL: http://localhost:3000');
  console.log('API Key: Generate from WebAI extension');
  console.log('Model: claude-web, gemini-web, bing-copilot, etc.');
  console.log('\n💡 Both /v1/ and direct paths are now supported!');
}

// Run tests
testEndpoints().catch(console.error);