# WebAI - OpenAI Compatible Local Server

A production-ready browser extension that provides **OpenAI-compatible API access** to various AI models (<PERSON>, <PERSON>, Bing Copilot, DeepSeek, Perplexity) running locally through your browser sessions.

## 🚀 Features

- 🤖 **OpenAI-Compatible API**: Drop-in replacement for OpenAI API endpoints
- 🔗 **Multiple AI Models**: Support for Claude, Gemini, Bing Copilot, DeepSeek, and Perplexity
- 🔐 **Temporary API Keys**: Generate secure temporary API keys for client applications
- ⚙️ **Rich Settings**: Comprehensive settings page for model configuration
- 🌐 **Local Server**: Runs entirely in your browser, no external dependencies
- 🔒 **Privacy First**: Your conversations stay in your browser sessions

## 📊 Supported Models

| Model        | Provider   | Image Support | Reasoning |
| ------------ | ---------- | ------------- | --------- |
| <PERSON> (Web) | Anthropic  | ✅            | ✅        |
| Gemini (Web) | Google     | ✅            | ✅        |
| Bing Copilot | Microsoft  | ✅            | ✅        |
| DeepSeek     | DeepSeek   | ✅            | ✅        |
| Perplexity   | Perplexity | ✅            | ✅        |

## 📦 Installation

### 🚀 Quick Start (Pre-built Release)

Download the latest release from the releases page and follow the installation instructions below.

#### Basic Setup

1. **Install and activate** the extension
2. **Click the extension icon** in the toolbar
3. **Start the server** using the "Start Server" button
4. **Generate an API key** if needed
5. **Copy the server URL** (usually `http://localhost:3000`)

#### Usage with AI Clients

For tools like Cline, Roo Code, Cursor, or other OpenAI-compatible clients:

1. **Provider**: Select "OpenAI Compatible" or "Custom OpenAI"
2. **Base URL**: `http://localhost:3000`
3. **API Key**: Use the generated key from the extension
4. **Model**: Choose from available models:
   - `claude-web`
   - `gemini-web`
   - `bing-copilot`
   - `deepseek-web`
   - `perplexity-web`

### From Source

#### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Chrome/Edge browser

#### Installation

1. **Clone or download** this repository
2. **Install dependencies:**
   ```bash
   cd webAI
   npm install
   ```
3. **Build the extension:**
   ```bash
   npm run build
   ```
4. **Load in browser:**
   - Open `chrome://extensions/` (or `edge://extensions/`)
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the `dist` folder inside `WebAI`

#### Development Build

For development with file watching:

```bash
npm run watch
```

## ⚙️ Configuration

### Extension Settings

Access the full settings page by:

- Right-clicking the extension icon → "Options"
- Or clicking "Advanced Settings" in the popup

**Available Settings:**

- **Server Configuration**: Port, auto-start, CORS
- **Default Model**: Choose your preferred default model
- **API Key Management**: Generate/revoke temporary API keys
- **Model-Specific Settings**: Configure each model individually
- **Security**: API key expiry, rate limiting, auth requirements
- **Advanced**: Debug mode, logging, data management

### Model Requirements

Before using each model, ensure you're logged into the respective services:

- **Claude**: Login to [claude.ai](https://claude.ai)
- **Gemini**: Login to [gemini.google.com](https://gemini.google.com)
- **Bing Copilot**: Login to [copilot.microsoft.com](https://copilot.microsoft.com)
- **DeepSeek**: Login to [chat.deepseek.com](https://chat.deepseek.com)
- **Perplexity**: Login to [perplexity.ai](https://perplexity.ai)

## 🔗 API Endpoints

The extension provides OpenAI-compatible endpoints:

### List Models

```http
GET http://localhost:3000/v1/models
```

### Chat Completions

```http
POST http://localhost:3000/v1/chat/completions
Content-Type: application/json
Authorization: Bearer <your-api-key>
```

**Example request:**

```json
{
  "model": "claude-web",
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": true
}
```

### Health Check

```http
GET http://localhost:3000/v1/health
```

### Authentication

Include your generated API key in the Authorization header:

```
Authorization: Bearer your-api-key-here
```

## 📝 Supported Request Parameters

### Standard OpenAI Parameters

- `model`: Model identifier (see available models)
- `messages`: Array of conversation messages
- `stream`: Boolean for streaming responses (recommended)
- `temperature`: Response randomness (0.0-2.0)
- `max_tokens`: Maximum response length
- `top_p`: Nucleus sampling parameter

### Extension-Specific Parameters

- `mode`: For models supporting multiple modes (chat/reasoning)
- `search_enabled`: Enable/disable web search (where supported)
- `image_support`: Handle image inputs (where supported)

## 🔒 Security

- **Temporary API Keys**: Keys expire after 24 hours by default
- **Local Only**: No data leaves your machine
- **Session-Based**: Uses your existing browser sessions
- **Revocable Keys**: Keys can be revoked at any time
- **Rate Limiting**: Request throttling protection
- **Localhost Only**: Optional security restriction

## 🎨 Customization

The extension is highly customizable:

### Model Configuration

Each model can be configured individually:

- **Claude**: Style preferences, conversation settings
- **Gemini**: Safety settings, response length
- **Bing Copilot**: Mode selection (Chat/Reasoning)
- **DeepSeek**: Search settings, reasoning mode
- **Perplexity**: Focus area, source preferences

## 🛠 Troubleshooting

### Server Won't Start

- Check if another service is using port 3000
- Try changing the port in settings
- Check browser console for errors
- Verify the extension is enabled

### Model Not Responding

- Verify you're logged into the service (Claude.ai, Gemini, etc.)
- Check network connectivity
- Try refreshing the service's website
- Generate a new API key
- Check model-specific settings

### Authentication Errors

- Ensure API key is correctly copied
- Check if key has expired
- Regenerate API key if needed
- Verify request headers

### CORS Errors

- Ensure CORS is enabled in settings
- Check if localhost-only mode is affecting access
- Verify request headers

### Debug Mode

Enable debug mode for detailed logs:

1. Open extension options
2. Enable "Debug Mode"
3. Check browser console for detailed logs

## 💻 Development

### Project Structure

```
webAI/
├── src/                 # Source code
│   ├── background.js    # Service worker + API server
│   ├── popup.js         # Extension popup
│   ├── options.js       # Settings page
│   └── api-server.js    # Local API server
├── styles/              # CSS files
├── lib/                 # AI Models Bridge library
├── icons/               # Extension icons
├── dist/                # Built files (generated)
├── manifest.json        # Extension manifest
├── popup.html           # Popup interface
├── options.html         # Settings interface
└── rules.json           # Request rules
```

### Building from Source

```bash
# Install dependencies
npm install

# Development build with watching
npm run watch

# Production build
npm run build

# Package for distribution
npm run package
```

This creates a `webai-extension.zip` file ready for distribution.

### Available Scripts

- `npm run build` - Production build
- `npm run dev` - Development build
- `npm run watch` - Development with file watching
- `npm run package` - Create distribution package
- `npm run clean` - Clean build artifacts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 📞 Support

- **Issues**: Report bugs on GitHub Issues
- **Documentation**: Check the repository for detailed guides/instructions
- **Updates**: Follow releases for new features
- **Security**: API keys are stored locally, keys expire automatically, all communication stays within your browser

## 📋 Changelog

### v1.0.0

- Initial release
- OpenAI-compatible API endpoints
- Support for 5 major AI models
- Comprehensive settings interface
- Temporary API key system
- Local server implementation
- Production-ready documentation
