{"name": "AI Models Bridge Demo", "description": "Demo extension for AI Models Bridge library", "manifest_version": 3, "version": "1.0.0", "action": {"default_popup": "popup.html", "default_icon": {}}, "background": {"service_worker": "dist/background.bundle.js", "type": "module"}, "permissions": ["storage", "declarativeNetRequest", "cookies", "scripting", "tabs", "windows", "webRequest"], "host_permissions": ["https://*.openai.com/*", "https://chatgpt.com/*", "https://gemini.google.com/*", "https://*.claude.ai/", "https://*.baichuan-ai.com/", "https://*.openrouter.ai/", "https://*.anthropic.com/", "https://copilot.microsoft.com/*", "wss://copilot.microsoft.com/*", "https://*.microsoft.com/*", "https://*.bing.com/*", "https://*.deepseek.com/*"], "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["assets/sha3_wasm_bg.7b9ca65ddd.wasm"], "matches": ["<all_urls>"]}]}