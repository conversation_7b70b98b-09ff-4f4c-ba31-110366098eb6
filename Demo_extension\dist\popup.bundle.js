(()=>{const{GeminiWebModel:e,<PERSON><PERSON>ebModel:t,ClaudeWebModel:n,PerplexityWebModel:a,DeepseekWebModel:o,AIModelError:s,ErrorCode:i,configureAuth:l}=window.AIModelsBridge,d=document.getElementById("model-select"),r=document.getElementById("api-key-container");let c=document.getElementById("api-key");const m=document.getElementById("prompt"),u=document.getElementById("send-button"),g=document.getElementById("response"),p=document.getElementById("chat-container"),h=document.getElementById("load-conversations"),y=document.getElementById("conversations-list"),v=document.getElementById("new-thread-btn"),f=document.getElementById("image-preview"),E=document.createElement("div"),b=document.createElement("div"),C=document.getElementById("claude-styles-container"),T=document.getElementById("claude-style-select"),w=document.getElementById("edit-title-btn"),x=document.createElement("button"),L=document.getElementById("thread-title-container"),k=document.createElement("button"),M=document.createElement("button"),S=document.createElement("button"),I=document.getElementById("perplexity-options-container"),N=document.getElementById("perplexity-model-select"),H=(document.getElementById("perplexity-focus-select"),document.getElementById("perplexity-sources-container"),document.getElementById("perplexity-sources-checkboxes")),B=document.getElementById("file-upload-btn");function $(){const e=d.value;"gemini-web"===e||"bing-web"===e?B.setAttribute("accept","image/*"):B.removeAttribute("accept")}d.addEventListener("change",$),window.addEventListener("DOMContentLoaded",$),x.id="share-conversation-btn",x.className="icon-btn",x.title="Share conversation",x.innerHTML="🔗",x.style.display="none",k.id="delete-conversation-btn",k.className="icon-btn",k.title="Delete conversation",k.innerHTML="🗑️",k.style.display="none",M.className="icon-btn",M.id="perplexity-private-btn",M.title="Make conversation private",M.innerHTML="🔒",M.style.display="none",S.id="get-convo-data-btn",S.className="icon-btn",S.title="Get Conversation Data (Log to Console)",S.innerHTML="📊",S.style.display="none",L&&(L.appendChild(x),L.appendChild(M),L.appendChild(k),L.appendChild(S)),E.id="bing-mode-toggle",E.className="hidden",E.innerHTML='\n  <div class="mode-toggle-container">\n    <label class="mode-toggle">\n      <span>Mode:</span>\n      <select id="bing-mode-select">\n        <option value="chat" selected>Chat</option>\n        <option value="reasoning">Reasoning</option>\n      </select>\n      <span class="mode-info" title="Chat is conversational, Reasoning is more analytical">ⓘ</span>\n    </label>\n  </div>\n',b.id="deepseek-toggles",b.className="hidden",b.innerHTML='\n  <div class="deepseek-toggle-row" style="display: flex; gap: 16px; justify-content: space-between;">\n    <div class="mode-toggle-container" style="flex:1;">\n      <label class="mode-toggle" style="width:100%;">\n        <span>Search:</span>\n        <select id="deepseek-search-select" style="margin-left: 6px;">\n          <option value="enabled" selected>Enabled</option>\n          <option value="disabled">Disabled</option>\n        </select>\n        <span class="mode-info" title="Enable or disable Deepseek\'s internet search features">ⓘ</span>\n      </label>\n    </div>\n    <div class="mode-toggle-container" style="flex:1; text-align:right;">\n      <label class="mode-toggle" style="width:100%;">\n        <span>Mode:</span>\n        <select id="deepseek-mode-select" style="margin-left: 6px;">\n          <option value="chat" selected>Chat</option>\n          <option value="reasoning">Reasoning</option>\n        </select>\n        <span class="mode-info" title="Chat is conversational, Reasoning is more analytical">ⓘ</span>\n      </label>\n    </div>\n  </div>\n',document.addEventListener("change",(e=>{e.target&&"deepseek-mode-select"===e.target.id&&chrome.storage.local.set({deepseekMode:e.target.value}),e.target&&"deepseek-search-select"===e.target.id&&chrome.storage.local.set({deepseekSearch:e.target.value})}));const D=document.querySelector(".input-container");if(D)D.insertBefore(E,document.querySelector(".button-container")),D.insertBefore(b,document.querySelector(".button-container"));else{const e=m.parentElement;e&&(e.insertBefore(E,m),e.insertBefore(b,m))}let A=null,R=[],W=null,j=null,F=null,U=null,O="internet",P=["web"];function G(e,t="info",n=3e3){const a=function(){let e=document.getElementById("toast-container");return e||(e=document.createElement("div"),e.id="toast-container",e.style.position="fixed",e.style.bottom="20px",e.style.right="20px",e.style.zIndex="1000",document.body.appendChild(e)),e}(),o=document.createElement("div");return o.className=`toast toast-${t}`,o.innerHTML=e,o.style.backgroundColor="info"===t?"#3498db":"#e74c3c",o.style.color="white",o.style.padding="10px 15px",o.style.borderRadius="4px",o.style.marginTop="10px",o.style.boxShadow="0 2px 5px rgba(0,0,0,0.2)",o.style.transition="all 0.3s ease",o.style.opacity="0",a.appendChild(o),setTimeout((()=>{o.style.opacity="1"}),10),setTimeout((()=>{o.style.opacity="0",setTimeout((()=>{a.removeChild(o)}),300)}),n),o}function Z(t){const n=document.getElementById("current-thread-title");if(n){let a=t||"New Conversation",o="";A instanceof e&&A.currentThread?.metadata?.emoji&&(o=A.currentThread.metadata.emoji,a.startsWith(o+" ")||(a=`${o} ${a}`)),n.textContent=a,n.style.display=t&&W?"":"none"}}async function _(){const e=d.value;await chrome.storage.local.set({selectedModel:e}),W=null,await chrome.storage.local.remove(["currentThreadId"]),A=ae(),y.classList.add("hidden"),C.classList.add("hidden"),E.classList.add("hidden"),b.classList.add("hidden"),T.innerHTML="",I.classList.add("hidden"),"claude-web"===e?(r.classList.add("hidden"),C.classList.remove("hidden"),A&&J()):"bing-web"===e?(r.classList.add("hidden"),E.classList.remove("hidden")):"gemini-web"===e?r.classList.add("hidden"):"perplexity-web"===e?(r.classList.add("hidden"),I.classList.remove("hidden"),A&&ce()):"deepseek-web"===e&&(r.classList.add("hidden"),b.classList.remove("hidden")),ie(),Z(null),K()}function K(){console.log("Updating button visibility. Model:",A?.getName(),"Thread ID:",W),w.style.display="none",x.style.display="none",k.style.display="none",M.style.display="none",S.style.display="none";const t=!!W,s=A?.currentThread?.messages&&A.currentThread.messages.length>0,i=A?.currentThread?.messages&&A.currentThread.messages.length>=2;console.log(`isThreadLoaded: ${t}, hasMessages: ${s}, hasMultipleMessages: ${i}`),t&&(A instanceof n?(w.style.display=s?"":"none",x.style.display=i?"":"none",k.style.display=s?"":"none",S.style.display=s?"":"none"):A instanceof a?(w.style.display=s?"":"none",x.style.display=i?"":"none",k.style.display=s?"":"none",M.style.display=i?"":"none"):A instanceof e?(w.style.display=s?"":"none",x.style.display=i?"":"none",M.style.display=i?"":"none",k.style.display=s?"":"none",S.style.display=s?"":"none"):A instanceof o&&(k.style.display=s?"":"none",S.style.display=s?"":"none",w.style.display=s?"":"none",x.style.display="none",M.style.display="none")),console.log("Button Visibility:",{edit:w.style.display,share:x.style.display,delete:k.style.display,unshare:M.style.display,getData:S.style.display})}function q(){if(!A||!W)return void G("No conversation selected to rename","error");const t=document.getElementById("current-thread-title");let n=t.textContent||"New Conversation";if(A instanceof e&&A.currentThread?.metadata?.emoji){const e=A.currentThread.metadata.emoji;n=n.replace(`${e} `,"")}t.style.display="none",w.style.display="none",x.style.display="none",k.style.display="none",M.style.display="none",S.style.display="none";const a=document.getElementById("thread-title-container"),o=document.createElement("div");o.className="title-edit-container";let s=`\n    <input type="text" id="title-input" value="${n}" placeholder="Enter conversation title" style="flex-grow: 1; margin-right: 5px;">\n  `;A instanceof e&&(s+=`\n      <input type="text" id="emoji-input" value="${A.currentThread?.metadata?.emoji||""}" placeholder="Emoji" maxlength="2" style="width: 60px; margin-right: 5px; text-align: center;">\n    `),s+='\n    <button id="save-title-btn" class="btn">Save</button>\n    <button id="cancel-title-btn" class="btn">Cancel</button>\n  ',o.innerHTML=s,o.style.display="flex",o.style.alignItems="center",a.appendChild(o);const i=document.getElementById("title-input");i.focus(),i.select(),document.getElementById("save-title-btn").addEventListener("click",Y),document.getElementById("cancel-title-btn").addEventListener("click",V),i.addEventListener("keyup",(e=>{"Enter"===e.key?Y():"Escape"===e.key&&V()}))}function V(){const e=document.getElementById("thread-title-container"),t=e.querySelector(".title-edit-container");t&&e.removeChild(t),document.getElementById("current-thread-title").style.display="",w.style.display="",K()}async function Y(){const t=document.getElementById("title-input").value.trim();if(!t)return void G("Title cannot be empty","error");const s=document.getElementById("save-title-btn"),i=s.textContent;s.textContent="Saving...",s.disabled=!0;try{if(A instanceof n||A instanceof a||A instanceof o)await A.editTitle(t);else{if(!(A instanceof e))return G("Title editing is not supported for this model.","error"),s.textContent=i,s.disabled=!1,void V();{const e=document.getElementById("emoji-input"),n=e?e.value.trim():void 0;await A.editTitle(t,n)}}Z(t),A.currentThread&&(A.currentThread.title=t),G("Title updated successfully","info"),V()}catch(e){console.error("Error updating title:",e),G(`Failed to update title: ${oe(e)}`,"error"),s.textContent=i,s.disabled=!1}}async function z(e=!0){try{console.log("Creating new thread...",e),y.classList.add("hidden"),console.log("eeeeeeeeeeeee:",A),A||(A=ae()),await A.initNewThread(),console.log("New thread created:",A),A.currentThread?(W=A instanceof o&&A.currentThread.metadata?.conversationId||A.currentThread.id,await chrome.storage.local.set({currentThreadId:W}),Z(null)):Z(null),ie(),K(),e&&se()}catch(e){console.error("Error creating thread:",e);const t=document.createElement("div");t.className="message assistant-message",t.textContent=`Error creating new thread: ${e.message}`,p.appendChild(t)}}async function J(){try{if(!(A&&A instanceof n))return;const e=await A.getStyles();if(!e)return void console.error("Failed to get Claude styles");if(j=e,e.defaultStyles&&Array.isArray(e.defaultStyles)){const t=document.createElement("option");t.disabled=!0,t.textContent="───Styles───",T.appendChild(t),e.defaultStyles.forEach((e=>{const t=document.createElement("option");t.value=e.key,t.textContent=e.name,T.appendChild(t)}))}if(e.customStyles&&Array.isArray(e.customStyles)){if(e.defaultStyles&&e.defaultStyles.length>0){const e=document.createElement("option");e.disabled=!0,e.textContent="──────────",T.appendChild(e)}e.customStyles.forEach((e=>{const t=document.createElement("option");t.value=e.key,t.textContent=e.name,T.appendChild(t)}))}chrome.storage.local.get(["claudeStyleKey"],(e=>{e.claudeStyleKey&&Array.from(T.options).some((t=>t.value===e.claudeStyleKey))&&(T.value=e.claudeStyleKey,F=e.claudeStyleKey)}))}catch(e){console.error("Error loading Claude styles:",e)}}function Q(){if(!A||!A.currentThread)return;ie(),Z(A.currentThread.title);const e=A.currentThread.messages;K(),e&&e.length>0?(e.forEach(((e,t)=>{const n=document.createElement("div");if(n.className="message "+("user"===e.role?"user-message":"assistant-message"),n.dataset.messageIndex=t,"assistant"===e.role){if(e.reasoningContent){const a=document.createElement("div");a.id=`reasoning-${e.id||t}`,a.className="message-reasoning-container collapsed";const o=e.metadata?.reasoningTimeSecs,s=o?`Thought for ${o} second${1===o?"":"s"}`:"Thinking Process";a.innerHTML=`\n               <div class="message-reasoning-header">\n                 <span class="thought-icon">🧠</span>\n                 <span>${s}</span>\n                 <span class="toggle-icon">▼</span>\n               </div>\n               <div class="message-reasoning-content">\n                 ${e.reasoningContent}\n               </div>\n              `,n.appendChild(a)}const a=document.createElement("div");a.className="message-text-content",a.innerHTML=e.content,n.appendChild(a)}else if("user"===e.role)if(e.metadata?.imageDataUrl||e.metadata?.imageUrl){const t=e.metadata.imageDataUrl||e.metadata.imageUrl;n.innerHTML=`\n              <div class="message-image-container">\n                <img src="${t}" alt="User uploaded image" class="message-image" onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOTk5Ij5JbWFnZSB1bmF2YWlsYWJsZTwvdGV4dD48L3N2Zz4=';">\n              </div>\n              <div class="message-text-content">${e.content}</div>\n            `}else{const t=document.createElement("div");t.className="message-text-content",t.textContent=e.content,n.appendChild(t)}else{const t=document.createElement("div");t.className="message-text-content",t.textContent=e.content,n.appendChild(t)}if(p.appendChild(n),"assistant"===e.role&&e.metadata?.suggestedResponses){const t=document.createElement("div");t.className="suggestions-container",e.metadata.suggestedResponses.forEach((e=>{const n=document.createElement("button");n.className="suggestion-btn",n.textContent=e,n.addEventListener("click",(()=>{m.value=e,te()})),t.appendChild(n)})),p.appendChild(t)}})),g.scrollTop=g.scrollHeight):se()}function X(){if(f.innerHTML="",0===R.length)return B.value="",void f.classList.add("hidden");f.classList.remove("hidden"),R.forEach(((e,t)=>{const n=document.createElement("div");n.className="image-preview-item";let a="";if(e.type.startsWith("image/")){const n=new FileReader;n.onload=function(n){a=`\n        <img src="${n.target.result}" alt="Selected image ${t+1}" class="preview-image">\n        <div class="file-preview-name">${e.name}</div>\n        `,o()},n.readAsDataURL(e)}else a=`\n        <div class="file-preview-icon">📄</div>\n        <div class="file-preview-name">${e.name}</div>\n      `,o();function o(){n.innerHTML=`\n        ${a}\n        <button class="remove-btn" data-index="${t}" title="Remove file">&times;</button>\n      `,n.querySelector(".remove-btn").onclick=()=>{R.splice(t,1),X()}}f.appendChild(n)}))}function ee(e){const t=e.target.files;if(!t||0===t.length)return;let n=R.length;const a=Array.from(t);if(n+a.length>4)return G("Maximum of 4 files allowed.","error"),void(B.value="");const o=d.value,s="gemini-web"===o||"bing-web"===o;if(a.forEach((e=>{!s||e.type.startsWith("image/")?R.push(e):G(`File "${e.name}" is not an image.`,"error")})),X(),"deepseek-web"===o){const e=document.getElementById("deepseek-search-select");R.length>0&&e?(e.disabled=!0,G("Attachments are enabled – disabling search.","info")):e&&(e.disabled=!1)}const i=document.getElementById("deepseek-search-select");i&&i.addEventListener("change",(e=>{"enabled"===i.value&&R.length>0&&(G("You can't enable search when attachments are added. Clearing attachments.","error"),R=[],X(),B.value="")}))}async function te(){const e=m.value.trim();if(e)try{u.disabled=!0,u.innerHTML='<span class="loading-spinner"></span>Sending...',A||(A=ae()),W||await z(!1);const t=document.createElement("div");t.className="message user-message";let n=`<div class="message-text">${m.value}</div>`;if(R.length>0){let e='<div class="message-image-container">';R.forEach(((t,n)=>{const a=URL.createObjectURL(t);e+=`<img src="${a}" alt="User uploaded image ${n+1}" class="message-image">`})),e+="</div>",n=e+n}t.innerHTML=n,p.appendChild(t),p.scrollTop=p.scrollHeight;const s=document.createElement("div");s.className="message assistant-message",s.innerHTML='<div class="message-text-content"><div class="loading">Thinking...</div></div>',p.appendChild(s),p.scrollTop=p.scrollHeight,m.value="",f.innerHTML="",f.classList.add("hidden");const i=[...R];let l;if(R=[],B.value="",console.log("Sending message to model:",e),console.log("Current model type:",A.getName()),"bing"===d.value&&!E.classList.contains("hidden")){const e=document.getElementById("bing-mode-select");e&&(l=e.value,console.log("Using Bing mode:",l))}console.log(F);const r={images:i,signal:null,mode:l,style_key:F,model:void 0,searchFocus:void 0,searchSources:void 0,onEvent:e=>{const t=document.querySelector(".message.assistant-message:last-of-type");switch(e.type){case"UPDATE_ANSWER":console.log("Assistant message update:",e.data),t&&t.classList.contains("hidden")&&t.classList.remove("hidden");const n=t?.querySelector(".message-text-content");if(n?n.innerHTML=e.data.text||'<div class="loading">Thinking...</div>':t&&(console.warn("Could not find .message-text-content to update."),t.innerHTML=e.data.text||'<div class="loading">Thinking...</div>'),g.scrollTop=g.scrollHeight,void 0!==e.data.reasoningContent&&t){let n=t.querySelector(".message-reasoning-container"),a=e.data.reasoningElapsedSecs;const o=a?`Thought for ${a} second${1===a?"":"s"}`:"Reasoning...";if(!n&&e.data.reasoningContent.trim())n=document.createElement("div"),n.className="message-reasoning-container collapsed",n.innerHTML=`\n                    <div class="message-reasoning-header">\n                      <span class="thought-icon">🧠</span>\n                      <span>${o}</span>\n                      <span class="toggle-icon">▼</span>\n                    </div>\n                    <div class="message-reasoning-content">${e.data.reasoningContent.trimStart()}</div>\n                  `,t.prepend(n);else if(n){const t=n.querySelector(".message-reasoning-content");t&&(t.innerHTML=e.data.reasoningContent);const s=n.querySelector(".message-reasoning-header span:nth-child(2)");s&&(s.textContent=o),a&&n.classList.remove("collapsed"),n.scrollTop=n.scrollHeight}}p.scrollTop=p.scrollHeight,g.scrollTop=g.scrollHeight;break;case"ERROR":const a=document.querySelector(".message.assistant-message:last-of-type");a&&!a.classList.contains("hidden")&&a.classList.add("hidden");const o=document.createElement("div");o.className="message error-message",o.textContent=`Error: ${e.error.message}`,p?(p.appendChild(o),p.scrollTop=p.scrollHeight):console.error("Chat container not found during error handling."),u?(u.disabled=!1,u.textContent="Send"):console.error("Send button not found during error handling.");break;case"DONE":u.disabled=!1,u.textContent="Send",R=[],K();break;case"TITLE_UPDATE":Z(e.data.title);break;case"SUGGESTED_RESPONSES":e.data.suggestions&&e.data.suggestions.length>0&&function(e){if(!e||!e.length)return;console.log("Displaying suggested responses:",e);const t=document.createElement("div");t.className="suggestions-container",e.forEach((e=>{const n=document.createElement("button");n.className="suggestion-btn",n.textContent=e,n.addEventListener("click",(()=>{m.value=e,te()})),t.appendChild(n)})),p.appendChild(t)}(e.data.suggestions)}}};if(A instanceof a&&(r.model=U||A.defaultModel,O=P.length>0?"internet":"writing",r.searchFocus=O,r.searchSources=P.length>0?P:void 0,console.log("Sending Perplexity Options:",{model:r.model,searchFocus:r.searchFocus,searchSources:r.searchSources})),A instanceof o){let e="chat";const t=document.getElementById("deepseek-mode-select");t&&(e=t.value);let n=!1;const a=document.getElementById("deepseek-search-select");a&&(n="enabled"===a.value),r.mode=e,r.searchEnabled=n}await A.sendMessage(e,r);const c=document.getElementById("deepseek-search-select");c&&(c.disabled=!1)}catch(e){console.error("Error sending message:",e);const t=document.createElement("div");t.className="message error-message",t.textContent=`Error: ${e.message||"Unknown error occurred"}`,p.appendChild(t),p.scrollTop=p.scrollHeight,u.disabled=!1,u.textContent="Send"}}async function ne(){try{y.classList.add("hidden"),A||(A=ae());const e=(await A.getAllThreads()).filter((e=>e.modelName===A.getName()));if(y.innerHTML="",y.classList.remove("hidden"),0===e.length){const e=document.createElement("div");return e.className="no-threads-msg",e.textContent="No conversations found",void y.appendChild(e)}e.sort(((e,t)=>t.updatedAt-e.updatedAt));const t=document.createElement("div");t.className="threads-header",t.textContent="Your Conversations",y.appendChild(t);const n=document.createElement("div");n.className="threads-container",y.appendChild(n),e.forEach((e=>{const t=document.createElement("div");t.className="thread-item",W===e.id&&t.classList.add("active-thread"),t.dataset.threadId=e.id;const a=document.createElement("div");a.className="thread-title",a.textContent=e.title||`Conversation ${new Date(e.createdAt).toLocaleString()}`,t.appendChild(a);const o=document.createElement("div");o.className="thread-date",o.textContent=`Last updated: ${function(e){const t=new Date,n=new Date(e),a=Math.floor((t-n)/1e3),o=Math.floor(a/60),s=Math.floor(o/60),i=Math.floor(s/24);if(a<60)return"just now";if(o<60)return`${o} ${1===o?"minute":"minutes"} ago`;if(s<5)return`${s} ${1===s?"hour":"hours"} ago`;const l=n.toLocaleTimeString(void 0,{hour:"2-digit",minute:"2-digit"});if(t.toDateString()===n.toDateString())return`today, at ${l}`;const d=new Date(t);if(d.setDate(t.getDate()-1),d.toDateString()===n.toDateString())return`yesterday, at ${l}`;const r=new Date(t);return r.setDate(t.getDate()-2),r.toDateString()===n.toDateString()?`day before yesterday, at ${l}`:i<7?`${n.toLocaleDateString(void 0,{weekday:"long"})}, at ${l}`:`${n.toLocaleDateString(void 0,{weekday:"long",day:"numeric",month:"long"})}, at ${l}`}(e.updatedAt)}`,t.appendChild(o),t.addEventListener("click",(async()=>{await async function(e){try{A||(A=ae()),await A.loadThread(e),W=e,await chrome.storage.local.set({currentThreadId:W}),Q();const t=document.getElementById("current-thread-title");t&&A.currentThread?.title&&(t.textContent=A.currentThread.title),console.log("Thread loaded successfully:",e)}catch(e){console.error("Error loading thread:",e),alert("Failed to load conversation: "+oe(e))}}(e.id),y.classList.add("hidden")})),n.appendChild(t)}))}catch(e){console.error("Error loading threads:",e),alert("Failed to load conversations: "+oe(e))}}function ae(){const s=d.value;switch(console.log(s),s){case"gemini-web":return new e;case"bing-web":return new t;case"claude-web":const s=new n;return setTimeout((async()=>await J()),500),s;case"perplexity-web":const i=new a;return setTimeout((async()=>await ce()),0),i;case"deepseek-web":return new o;default:throw new Error("Unknown model selected")}}function oe(e){return e instanceof s||e instanceof Error?e.message:String(e)}function se(){const e=document.createElement("div");e.className="welcome-message";const t=["What would you like to know today?","What's on your mind today?","Ask me anything...","How can I assist you today?","Ready when you are! Type a message to begin.","What are you curious about?","What would you like to explore today?"],n=t[Math.floor(Math.random()*t.length)];e.textContent=n,p.appendChild(e)}function ie(){p.innerHTML=""}async function le(){if(A&&W)if(A instanceof n||A instanceof a||A instanceof e)try{const e=x.innerHTML;x.innerHTML="⏳",x.disabled=!0,!function(e){const t=document.createElement("div");t.className="modal-container",t.style.position="fixed",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.style.backgroundColor="rgba(0, 0, 0, 0.5)",t.style.display="flex",t.style.justifyContent="center",t.style.alignItems="center",t.style.zIndex="1000";const n=document.createElement("div");n.className="modal-content",n.style.backgroundColor="white",n.style.padding="20px",n.style.borderRadius="8px",n.style.maxWidth="90%",n.style.width="400px",n.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)";const a=document.createElement("div");a.className="modal-header",a.style.display="flex",a.style.justifyContent="space-between",a.style.alignItems="center",a.style.marginBottom="15px";const o=document.createElement("h3");o.textContent="Conversation Shared!",o.style.margin="0";const s=document.createElement("button");s.innerHTML="×",s.style.background="none",s.style.border="none",s.style.fontSize="24px",s.style.cursor="pointer",s.style.padding="0 5px",s.onclick=()=>document.body.removeChild(t),a.appendChild(o),a.appendChild(s);const i=document.createElement("div");i.className="modal-body";const l=document.createElement("p");l.textContent="Your conversation has been shared. Anyone with this link can view it:";const d=document.createElement("div");d.style.display="flex",d.style.marginBottom="15px",d.style.marginTop="15px";const r=document.createElement("input");r.type="text",r.value=e,r.readOnly=!0,r.style.flexGrow="1",r.style.padding="8px",r.style.border="1px solid #ccc",r.style.borderRadius="4px 0 0 4px";const c=document.createElement("button");c.textContent="Copy",c.className="btn",c.style.borderRadius="0 4px 4px 0",c.style.margin="0",c.onclick=()=>{r.select(),document.execCommand("copy"),c.textContent="Copied!",setTimeout((()=>{c.textContent="Copy"}),2e3)},d.appendChild(r),d.appendChild(c);const m=document.createElement("button");m.textContent="Open in Browser",m.className="btn",m.style.width="100%",m.style.marginTop="10px",m.onclick=()=>{window.open(e,"_blank")},i.appendChild(l),i.appendChild(d),i.appendChild(m),n.appendChild(a),n.appendChild(i),t.appendChild(n),document.body.appendChild(t)}(await A.shareConversation()),x.innerHTML=e,x.disabled=!1}catch(e){console.error("Error sharing conversation:",e),G(`Failed to share conversation: ${oe(e)}`,"error"),x.innerHTML="🔗",x.disabled=!1}else G("Sharing is only supported for Claude, Gemini and Perplexity conversations","error");else G("No conversation selected to share","error")}function de(){if(!A||!W)return void G("No conversation selected to delete","error");let t="Are you sure you want to delete this conversation (this will only remove it locally)? This action cannot be undone.";A instanceof n||A instanceof a||A instanceof e||(t="Are you sure you want to delete this conversation? This action cannot be undone. This will remove it locally and from the AI model servers.");const o=document.createElement("div");o.className="modal-container";const s=document.createElement("div");s.className="modal-content",s.style.maxWidth="350px";const i=document.createElement("div");i.className="modal-header";const l=document.createElement("h3");l.textContent="Delete Conversation",l.style.color="#d32f2f";const d=document.createElement("button");d.innerHTML="×",d.style.background="none",d.style.border="none",d.style.fontSize="24px",d.style.cursor="pointer",d.style.padding="0 5px",d.onclick=()=>document.body.removeChild(o),i.appendChild(l),i.appendChild(d);const r=document.createElement("div");r.className="modal-body";const c=document.createElement("p");c.textContent=t;const m=document.createElement("div");m.style.display="flex",m.style.justifyContent="flex-end",m.style.marginTop="20px",m.style.gap="10px";const u=document.createElement("button");u.textContent="Cancel",u.className="btn",u.style.backgroundColor="#9e9e9e",u.onclick=()=>document.body.removeChild(o);const g=document.createElement("button");g.textContent="Delete",g.className="btn",g.style.backgroundColor="#d32f2f",g.onclick=()=>{document.body.removeChild(o),async function(){try{const e=k.innerHTML;if(k.innerHTML="⏳",k.disabled=!0,!A.currentThread||!A.currentThread.metadata)throw new Error("No thread metadata available");"function"==typeof A.deleteServerThreads?(await A.deleteServerThreads([A.currentThread.metadata.conversationId],!0,!1),G("Conversation deleted successfully (Server & Local)","info")):(await A.deleteThread(W,!1),G("Conversation deleted successfully (Local Only)","info")),W=null,await chrome.storage.local.remove(["currentThreadId"]),await z(!1),A.currentThread&&(W=A.currentThread.id,await chrome.storage.local.set({currentThreadId:W}),Z(A.currentThread.title)),k.innerHTML=e,k.disabled=!1}catch(e){console.error("Error deleting conversation:",e),G(`Failed to delete conversation: ${oe(e)}`,"error"),k.innerHTML="🗑️",k.disabled=!1}}()},m.appendChild(u),m.appendChild(g),r.appendChild(c),r.appendChild(m),s.appendChild(i),s.appendChild(r),o.appendChild(s),document.body.appendChild(o)}async function re(){if(A&&W)if(A instanceof a||A instanceof e)try{const e=M.innerHTML;M.innerHTML="⏳",M.disabled=!0,await A.unShareConversation()?G("Conversation set to private","info"):G("Failed to set conversation to private","error"),M.innerHTML=e,M.disabled=!1}catch(e){console.error("Error setting conversation to private:",e),G(`Failed to set conversation to private: ${oe(e)}`,"error"),M.innerHTML="🔒",M.disabled=!1}else G("This operation is only supported for Perplexity and Gemini conversations","error");else G("No conversation selected","error")}function ce(){if(A&&A instanceof a)try{const e=A.getModels();N.innerHTML="";for(const t in e){const e=document.createElement("option");e.value=t,e.textContent=t,N.appendChild(e)}N.value=U||A.defaultModel,U=N.value;const t=A.getSearchSources();H.innerHTML="";const n=document.createElement("div");n.className="sources-wrapper",H.appendChild(n),t.forEach((e=>{const t=document.createElement("label");t.className="toggle-switch-label",P.includes(e)&&t.classList.add("active");const a=document.createElement("input");a.type="checkbox",a.value=e,a.id=`source-${e}`,a.checked=P.includes(e),t.textContent=e.charAt(0).toUpperCase()+e.slice(1),t.appendChild(a),n.appendChild(t)})),O=P.length>0?"internet":"writing"}catch(e){console.error("Error loading Perplexity options:",e),G("Failed to load Perplexity options","error")}}async function me(){if(!(A instanceof e||A instanceof n||A instanceof o))return void G("Model not supported for getting conversation data.","error");if(!A||!W)return void G("Please select a conversation first.","error");const t=S.innerHTML;S.innerHTML="⏳",S.disabled=!0;try{let e;console.log(`Fetching conversation data for thread: ${W} using model: ${A.getName()}`),A instanceof o?(e=await A.getAllConversationsData(),console.log("Deepseek All Conversations Data Received:",e)):(e=await A.getConversationData(),console.log("Conversation Data Received:",e)),console.log("Conversation Data Received:",e),G("Conversation data logged to console.","info"),S.innerHTML=t,S.disabled=!1}catch(e){console.error("Error getting conversation data:",e),G(`Failed to get conversation data: ${oe(e)}`,"error"),S.innerHTML="📊",S.disabled=!1}}l({notifyTokenRefresh:!0}),document.addEventListener("DOMContentLoaded",(async function(){const e=await chrome.storage.local.get(["apiKey","selectedModel","currentThreadId"]);e.apiKey&&(c.value=e.apiKey),e.selectedModel&&(d.value=e.selectedModel);const{deepseekMode:t,deepseekSearch:n}=await chrome.storage.local.get(["deepseekMode","deepseekSearch"]),a=document.getElementById("deepseek-mode-select");a&&t&&(a.value=t);const o=document.getElementById("deepseek-search-select");o&&n&&(o.value=n),await _(),T.addEventListener("change",(async()=>{F=T.value,await chrome.storage.local.set({claudeStyleKey:F})})),N.addEventListener("change",(()=>{U=N.value})),H.addEventListener("click",(e=>{const t=e.target.closest(".toggle-switch-label");if(t){const e=t.querySelector('input[type="checkbox"]');if(e){const n=e.value;e.checked=!e.checked,e.checked?(P.includes(n)||P.push(n),t.classList.add("active")):(P=P.filter((e=>e!==n)),t.classList.remove("active")),O=P.length>0?"internet":"writing"}}})),c?.addEventListener("change",(async()=>{await chrome.storage.local.set({apiKey:c.value})})),d.addEventListener("change",_),B.addEventListener("change",ee),u.addEventListener("click",te),p.addEventListener("click",(function(e){const t=e.target.closest(".message-reasoning-header");if(t){const e=t.closest(".message-reasoning-container");e&&e.classList.toggle("collapsed")}})),h.addEventListener("click",ne),v.addEventListener("click",z),w.addEventListener("click",q),x.addEventListener("click",le),k.addEventListener("click",de),M.addEventListener("click",re),S.addEventListener("click",me),document.addEventListener(window.AIModelsBridge.AUTH_EVENTS.TOKEN_REFRESH_START,(e=>{e.detail.background||G("Refreshing auth token...","info")})),document.addEventListener(window.AIModelsBridge.AUTH_EVENTS.TOKEN_REFRESH_COMPLETE,(e=>{e.detail.background||G("Auth token refreshed!","info",2e3)})),document.addEventListener(window.AIModelsBridge.AUTH_EVENTS.TOKEN_REFRESH_ERROR,(e=>{G(`Auth error: ${e.detail.error}`,"error",5e3)})),e.currentThreadId?(W=e.currentThreadId,await async function(){try{A||(A=ae()),W?(await A.loadThread(W),Q()):await z()}catch(e){console.error("Error initializing model or loading thread:",e),p.innerHTML=`<div class="message error-message">Error: ${e.message}</div>`}}()):await ne(),$()}))})();