/**
 * Options page script for WebAI extension
 * Handles the comprehensive settings interface
 */
import browser from 'webextension-polyfill';

// DOM elements
let tabButtons, tabContents;
let serverPortInput, autoStartCheckbox, corsEnabledCheckbox;
let defaultModelSelect, defaultTemperatureInput, temperatureValueSpan, defaultMaxTokensInput;
let modelConfigs = {};
let apiKeysList;
let keyExpirySelect, requireAuthCheckbox, generateNewKeyBtn, revokeAllKeysBtn;
let localhostOnlyCheckbox, rateLimitInput;
let debugModeCheckbox, logLevelSelect;
let clearLogsBtn, exportLogsBtn, clearCacheBtn, resetSettingsBtn, autoCleanupCheckbox;
let exportSettingsBtn, importSettingsBtn, importFileInput;
let saveNotification, toastContainer;

// Available models
const AVAILABLE_MODELS = {
  'claude-web': { name: '<PERSON> (<PERSON>)', icon: '🤖' },
  'gemini-web': { name: '<PERSON> (Web)', icon: '💎' },
  'bing-copilot': { name: 'Bing Copilot', icon: '🔵' },
  'deepseek-web': { name: 'DeepSeek (Web)', icon: '🧠' },
  'perplexity-web': { name: 'Perplexity (Web)', icon: '🔍' }
};

// Initialize options page
document.addEventListener('DOMContentLoaded', async () => {
  initializeElements();
  attachEventListeners();
  await loadAllSettings();
  await updateApiKeysList();
  
  console.log('WebAI options page initialized');
});

function initializeElements() {
  // Tab navigation
  tabButtons = document.querySelectorAll('.tab-btn');
  tabContents = document.querySelectorAll('.tab-content');
  
  // General tab elements
  serverPortInput = document.getElementById('server-port');
  autoStartCheckbox = document.getElementById('auto-start-server');
  corsEnabledCheckbox = document.getElementById('cors-enabled');
  defaultModelSelect = document.getElementById('default-model');
  defaultTemperatureInput = document.getElementById('default-temperature');
  temperatureValueSpan = document.getElementById('temperature-value');
  defaultMaxTokensInput = document.getElementById('default-max-tokens');
  
  // Model configuration elements
  modelConfigs = {
    'claude-web': {
      enabled: document.getElementById('claude-enabled'),
      style: document.getElementById('claude-style'),
      status: document.getElementById('claude-status'),
      testBtn: document.getElementById('test-claude-btn')
    },
    'gemini-web': {
      enabled: document.getElementById('gemini-enabled'),
      imageSupport: document.getElementById('gemini-image-support'),
      status: document.getElementById('gemini-status'),
      testBtn: document.getElementById('test-gemini-btn')
    },
    'bing-copilot': {
      enabled: document.getElementById('bing-enabled'),
      mode: document.getElementById('bing-mode'),
      status: document.getElementById('bing-status'),
      testBtn: document.getElementById('test-bing-btn')
    },
    'deepseek-web': {
      enabled: document.getElementById('deepseek-enabled'),
      mode: document.getElementById('deepseek-mode'),
      search: document.getElementById('deepseek-search'),
      status: document.getElementById('deepseek-status'),
      testBtn: document.getElementById('test-deepseek-btn')
    },
    'perplexity-web': {
      enabled: document.getElementById('perplexity-enabled'),
      focus: document.getElementById('perplexity-focus'),
      status: document.getElementById('perplexity-status'),
      testBtn: document.getElementById('test-perplexity-btn')
    }
  };
  
  // Security tab elements
  apiKeysList = document.getElementById('api-keys-list');
  keyExpirySelect = document.getElementById('key-expiry');
  requireAuthCheckbox = document.getElementById('require-auth');
  generateNewKeyBtn = document.getElementById('generate-new-key-btn');
  revokeAllKeysBtn = document.getElementById('revoke-all-keys-btn');
  localhostOnlyCheckbox = document.getElementById('localhost-only');
  rateLimitInput = document.getElementById('rate-limit');
  
  // Advanced tab elements
  debugModeCheckbox = document.getElementById('debug-mode');
  logLevelSelect = document.getElementById('log-level');
  clearLogsBtn = document.getElementById('clear-logs-btn');
  exportLogsBtn = document.getElementById('export-logs-btn');
  clearCacheBtn = document.getElementById('clear-cache-btn');
  resetSettingsBtn = document.getElementById('reset-settings-btn');
  autoCleanupCheckbox = document.getElementById('auto-cleanup');
  
  // Header elements
  exportSettingsBtn = document.getElementById('export-settings-btn');
  importSettingsBtn = document.getElementById('import-settings-btn');
  importFileInput = document.getElementById('import-file-input');
  
  // Notification elements
  saveNotification = document.getElementById('save-notification');
  toastContainer = document.getElementById('toast-container');
}

function attachEventListeners() {
  // Tab navigation
  tabButtons.forEach(button => {
    button.addEventListener('click', () => switchTab(button.dataset.tab));
  });
  
  // General settings
  serverPortInput?.addEventListener('change', saveGeneralSettings);
  autoStartCheckbox?.addEventListener('change', saveGeneralSettings);
  corsEnabledCheckbox?.addEventListener('change', saveGeneralSettings);
  defaultModelSelect?.addEventListener('change', saveGeneralSettings);
  defaultTemperatureInput?.addEventListener('input', updateTemperatureDisplay);
  defaultTemperatureInput?.addEventListener('change', saveGeneralSettings);
  defaultMaxTokensInput?.addEventListener('change', saveGeneralSettings);
  
  // Model settings
  Object.entries(modelConfigs).forEach(([modelId, config]) => {
    Object.entries(config).forEach(([key, element]) => {
      if (element && key !== 'status' && key !== 'testBtn') {
        element.addEventListener('change', () => saveModelSettings(modelId));
      }
    });
    
    config.testBtn?.addEventListener('click', () => testModelConnection(modelId));
  });
  
  // Security settings
  keyExpirySelect?.addEventListener('change', saveSecuritySettings);
  requireAuthCheckbox?.addEventListener('change', saveSecuritySettings);
  generateNewKeyBtn?.addEventListener('click', generateNewApiKey);
  revokeAllKeysBtn?.addEventListener('click', confirmRevokeAllKeys);
  localhostOnlyCheckbox?.addEventListener('change', saveSecuritySettings);
  rateLimitInput?.addEventListener('change', saveSecuritySettings);
  
  // Advanced settings
  debugModeCheckbox?.addEventListener('change', saveAdvancedSettings);
  logLevelSelect?.addEventListener('change', saveAdvancedSettings);
  clearLogsBtn?.addEventListener('click', clearLogs);
  exportLogsBtn?.addEventListener('click', exportLogs);
  clearCacheBtn?.addEventListener('click', clearCache);
  resetSettingsBtn?.addEventListener('click', confirmResetSettings);
  autoCleanupCheckbox?.addEventListener('change', saveAdvancedSettings);
  
  // Import/Export
  exportSettingsBtn?.addEventListener('click', exportSettings);
  importSettingsBtn?.addEventListener('click', () => importFileInput?.click());
  importFileInput?.addEventListener('change', importSettings);
}

function switchTab(tabId) {
  // Update tab buttons
  tabButtons.forEach(btn => {
    btn.classList.toggle('active', btn.dataset.tab === tabId);
  });
  
  // Update tab contents
  tabContents.forEach(content => {
    content.classList.toggle('active', content.id === `${tabId}-tab`);
  });
}

async function loadAllSettings() {
  try {
    const settings = await browser.storage.local.get([
      'serverPort',
      'autoStart',
      'corsEnabled',
      'defaultModel',
      'defaultTemperature',
      'defaultMaxTokens',
      'modelSettings',
      'keyExpiry',
      'requireAuth',
      'localhostOnly',
      'rateLimit',
      'debugMode',
      'logLevel',
      'autoCleanup'
    ]);
    
    // Load general settings
    if (serverPortInput) serverPortInput.value = settings.serverPort || 3000;
    if (autoStartCheckbox) autoStartCheckbox.checked = settings.autoStart || false;
    if (corsEnabledCheckbox) corsEnabledCheckbox.checked = settings.corsEnabled !== false;
    if (defaultModelSelect) defaultModelSelect.value = settings.defaultModel || 'claude-web';
    if (defaultTemperatureInput) {
      defaultTemperatureInput.value = settings.defaultTemperature || 1.0;
      updateTemperatureDisplay();
    }
    if (defaultMaxTokensInput) defaultMaxTokensInput.value = settings.defaultMaxTokens || 2048;
    
    // Load model settings
    const modelSettings = settings.modelSettings || {};
    Object.entries(modelConfigs).forEach(([modelId, config]) => {
      const modelSetting = modelSettings[modelId] || {};
      
      if (config.enabled) config.enabled.checked = modelSetting.enabled !== false;
      if (config.style) config.style.value = modelSetting.style || 'default';
      if (config.imageSupport) config.imageSupport.checked = modelSetting.imageSupport !== false;
      if (config.mode) config.mode.value = modelSetting.mode || 'chat';
      if (config.search) config.search.checked = modelSetting.search !== false;
      if (config.focus) config.focus.value = modelSetting.focus || 'internet';
    });
    
    // Load security settings
    if (keyExpirySelect) keyExpirySelect.value = settings.keyExpiry || '24h';
    if (requireAuthCheckbox) requireAuthCheckbox.checked = settings.requireAuth !== false;
    if (localhostOnlyCheckbox) localhostOnlyCheckbox.checked = settings.localhostOnly !== false;
    if (rateLimitInput) rateLimitInput.value = settings.rateLimit || 60;
    
    // Load advanced settings
    if (debugModeCheckbox) debugModeCheckbox.checked = settings.debugMode || false;
    if (logLevelSelect) logLevelSelect.value = settings.logLevel || 'info';
    if (autoCleanupCheckbox) autoCleanupCheckbox.checked = settings.autoCleanup || false;
    
  } catch (error) {
    console.error('Failed to load settings:', error);
    showToast('Failed to load settings', 'error');
  }
}

function updateTemperatureDisplay() {
  if (defaultTemperatureInput && temperatureValueSpan) {
    temperatureValueSpan.textContent = defaultTemperatureInput.value;
  }
}

async function saveGeneralSettings() {
  try {
    const settings = {
      serverPort: parseInt(serverPortInput?.value) || 3000,
      autoStart: autoStartCheckbox?.checked || false,
      corsEnabled: corsEnabledCheckbox?.checked !== false,
      defaultModel: defaultModelSelect?.value || 'claude-web',
      defaultTemperature: parseFloat(defaultTemperatureInput?.value) || 1.0,
      defaultMaxTokens: parseInt(defaultMaxTokensInput?.value) || 2048
    };
    
    await browser.storage.local.set(settings);
    showSaveNotification();
  } catch (error) {
    console.error('Failed to save general settings:', error);
    showToast('Failed to save settings', 'error');
  }
}

async function saveModelSettings(modelId) {
  try {
    const result = await browser.storage.local.get(['modelSettings']);
    const modelSettings = result.modelSettings || {};
    
    const config = modelConfigs[modelId];
    if (!config) return;
    
    modelSettings[modelId] = {
      enabled: config.enabled?.checked !== false,
      style: config.style?.value || 'default',
      imageSupport: config.imageSupport?.checked !== false,
      mode: config.mode?.value || 'chat',
      search: config.search?.checked !== false,
      focus: config.focus?.value || 'internet'
    };
    
    await browser.storage.local.set({ modelSettings });
    showSaveNotification();
  } catch (error) {
    console.error(`Failed to save ${modelId} settings:`, error);
    showToast('Failed to save model settings', 'error');
  }
}

async function saveSecuritySettings() {
  try {
    const settings = {
      keyExpiry: keyExpirySelect?.value || '24h',
      requireAuth: requireAuthCheckbox?.checked !== false,
      localhostOnly: localhostOnlyCheckbox?.checked !== false,
      rateLimit: parseInt(rateLimitInput?.value) || 60
    };
    
    await browser.storage.local.set(settings);
    showSaveNotification();
  } catch (error) {
    console.error('Failed to save security settings:', error);
    showToast('Failed to save security settings', 'error');
  }
}

async function saveAdvancedSettings() {
  try {
    const settings = {
      debugMode: debugModeCheckbox?.checked || false,
      logLevel: logLevelSelect?.value || 'info',
      autoCleanup: autoCleanupCheckbox?.checked || false
    };
    
    await browser.storage.local.set(settings);
    showSaveNotification();
  } catch (error) {
    console.error('Failed to save advanced settings:', error);
    showToast('Failed to save advanced settings', 'error');
  }
}

async function testModelConnection(modelId) {
  const config = modelConfigs[modelId];
  if (!config?.testBtn || !config?.status) return;
  
  try {
    config.testBtn.disabled = true;
    config.testBtn.textContent = 'Testing...';
    config.status.textContent = 'Testing...';
    config.status.className = 'model-status testing';
    
    // Simulate connection test (would normally test actual model connection)
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // For demo purposes, randomly succeed or fail
    const success = Math.random() > 0.3;
    
    if (success) {
      config.status.textContent = 'Connected';
      config.status.className = 'model-status connected';
      showToast(`${AVAILABLE_MODELS[modelId].name} connection successful`, 'success');
    } else {
      config.status.textContent = 'Connection Failed';
      config.status.className = 'model-status error';
      showToast(`${AVAILABLE_MODELS[modelId].name} connection failed`, 'error');
    }
  } catch (error) {
    config.status.textContent = 'Error';
    config.status.className = 'model-status error';
    showToast(`Error testing ${AVAILABLE_MODELS[modelId].name}`, 'error');
  } finally {
    config.testBtn.disabled = false;
    config.testBtn.textContent = 'Test Connection';
  }
}

async function updateApiKeysList() {
  if (!apiKeysList) return;
  
  try {
    const response = await browser.runtime.sendMessage({ type: 'GET_API_KEYS' });
    
    if (response.success) {
      renderApiKeys(response.data);
    } else {
      apiKeysList.innerHTML = '<p>Failed to load API keys</p>';
    }
  } catch (error) {
    console.error('Failed to load API keys:', error);
    apiKeysList.innerHTML = '<p>Error loading API keys</p>';
  }
}

function renderApiKeys(keys) {
  if (!apiKeysList) return;
  
  if (keys.length === 0) {
    apiKeysList.innerHTML = '<p class="no-keys">No API keys generated</p>';
    return;
  }
  
  apiKeysList.innerHTML = keys.map(key => {
    const createdDate = new Date(key.createdAt).toLocaleString();
    const expiresText = key.expiresAt 
      ? new Date(key.expiresAt).toLocaleString()
      : 'Never';
    
    return `
      <div class="api-key-item">
        <div class="key-header">
          <span class="key-id">${key.id.substring(0, 8)}...${key.id.substring(-4)}</span>
          <span class="key-status ${key.expiresAt && Date.now() > key.expiresAt ? 'expired' : 'active'}">
            ${key.expiresAt && Date.now() > key.expiresAt ? 'Expired' : 'Active'}
          </span>
        </div>
        <div class="key-details">
          <small>Created: ${createdDate}</small>
          <small>Expires: ${expiresText}</small>
          <small>Used: ${key.usageCount || 0} times</small>
        </div>
        <div class="key-actions">
          <button class="btn btn-small" onclick="copyApiKey('${key.id}')">Copy</button>
          <button class="btn btn-small btn-danger" onclick="revokeApiKey('${key.id}')">Revoke</button>
        </div>
      </div>
    `;
  }).join('');
}

async function generateNewApiKey() {
  try {
    generateNewKeyBtn.disabled = true;
    generateNewKeyBtn.textContent = 'Generating...';
    
    const expiryMs = parseExpiryValue(keyExpirySelect?.value || '24h');
    
    const response = await browser.runtime.sendMessage({
      type: 'GENERATE_API_KEY',
      expiryMs
    });
    
    if (response.success) {
      await updateApiKeysList();
      showToast('New API key generated', 'success');
    } else {
      showToast(`Failed to generate API key: ${response.error}`, 'error');
    }
  } catch (error) {
    console.error('Failed to generate API key:', error);
    showToast('Failed to generate API key', 'error');
  } finally {
    generateNewKeyBtn.disabled = false;
    generateNewKeyBtn.textContent = 'Generate New API Key';
  }
}

function parseExpiryValue(expiry) {
  switch (expiry) {
    case '1h': return 60 * 60 * 1000;
    case '24h': return 24 * 60 * 60 * 1000;
    case '7d': return 7 * 24 * 60 * 60 * 1000;
    case '30d': return 30 * 24 * 60 * 60 * 1000;
    case 'never': return null;
    default: return 24 * 60 * 60 * 1000;
  }
}

function confirmRevokeAllKeys() {
  if (confirm('Are you sure you want to revoke all API keys? This action cannot be undone.')) {
    revokeAllApiKeys();
  }
}

async function revokeAllApiKeys() {
  try {
    const response = await browser.runtime.sendMessage({ type: 'GET_API_KEYS' });
    
    if (response.success) {
      for (const key of response.data) {
        await browser.runtime.sendMessage({
          type: 'REVOKE_API_KEY',
          key: key.id
        });
      }
      
      await updateApiKeysList();
      showToast('All API keys revoked', 'info');
    }
  } catch (error) {
    console.error('Failed to revoke all keys:', error);
    showToast('Failed to revoke all keys', 'error');
  }
}

// Global functions for inline event handlers
window.copyApiKey = function(keyId) {
  navigator.clipboard.writeText(keyId).then(() => {
    showToast('API key copied to clipboard', 'success');
  }).catch(() => {
    showToast('Failed to copy API key', 'error');
  });
};

window.revokeApiKey = async function(keyId) {
  try {
    const response = await browser.runtime.sendMessage({
      type: 'REVOKE_API_KEY',
      key: keyId
    });
    
    if (response.success) {
      await updateApiKeysList();
      showToast('API key revoked', 'info');
    } else {
      showToast('Failed to revoke API key', 'error');
    }
  } catch (error) {
    showToast('Failed to revoke API key', 'error');
  }
};

// Advanced features
function clearLogs() {
  console.clear();
  showToast('Console logs cleared', 'info');
}

function exportLogs() {
  // This would export console logs if we had a way to capture them
  showToast('Log export feature coming soon', 'info');
}

function clearCache() {
  browser.storage.local.clear(['chat_threads', 'model_cache']).then(() => {
    showToast('Cache cleared', 'info');
  }).catch(() => {
    showToast('Failed to clear cache', 'error');
  });
}

function confirmResetSettings() {
  if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
    resetAllSettings();
  }
}

async function resetAllSettings() {
  try {
    await browser.storage.local.clear();
    await loadAllSettings();
    await updateApiKeysList();
    showToast('All settings reset to defaults', 'info');
  } catch (error) {
    console.error('Failed to reset settings:', error);
    showToast('Failed to reset settings', 'error');
  }
}

function exportSettings() {
  browser.storage.local.get().then(settings => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const a = document.createElement('a');
    a.href = URL.createObjectURL(dataBlob);
    a.download = `webai-settings-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    showToast('Settings exported', 'success');
  }).catch(() => {
    showToast('Failed to export settings', 'error');
  });
}

function importSettings(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const settings = JSON.parse(e.target.result);
      browser.storage.local.set(settings).then(() => {
        loadAllSettings();
        updateApiKeysList();
        showToast('Settings imported successfully', 'success');
      }).catch(() => {
        showToast('Failed to import settings', 'error');
      });
    } catch (error) {
      showToast('Invalid settings file', 'error');
    }
  };
  reader.readAsText(file);
}

// UI helper functions
function showSaveNotification() {
  if (!saveNotification) return;
  
  saveNotification.classList.add('show');
  setTimeout(() => {
    saveNotification.classList.remove('show');
  }, 2000);
}

function showToast(message, type = 'info') {
  if (!toastContainer) return;
  
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.textContent = message;
  
  toastContainer.appendChild(toast);
  
  setTimeout(() => toast.classList.add('show'), 100);
  
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => {
      if (toast.parentNode) {
        toastContainer.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

console.log('WebAI options script loaded');