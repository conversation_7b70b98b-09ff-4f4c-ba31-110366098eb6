/* WebAI Options Page Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo h1 {
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Navigation Tabs */
.nav-tabs {
  background: white;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  padding: 0 32px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.tab-btn:hover {
  color: #495057;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

/* Main Content */
.main-content {
  padding: 32px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Settings Sections */
.settings-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.settings-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Setting Groups */
.setting-group {
  margin-bottom: 20px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  display: block;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.setting-group input:focus,
.setting-group select:focus,
.setting-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.help-text {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
  font-style: italic;
}

/* Checkbox Styling */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #495057;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #ced4da;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #007bff;
  border-color: #007bff;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Range Input Styling */
input[type="range"] {
  width: 200px;
  margin-right: 12px;
}

.range-value {
  font-weight: 600;
  color: #007bff;
  min-width: 40px;
  display: inline-block;
}

/* Model Configuration */
.model-config {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.model-config h3 {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-icon {
  font-size: 20px;
}

.model-status {
  margin-left: auto;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  background: #6c757d;
  color: white;
}

.model-status.connected {
  background: #28a745;
}

.model-status.error {
  background: #dc3545;
}

.model-status.testing {
  background: #ffc107;
  color: #212529;
}

/* API Keys List */
.api-keys-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.api-key-item {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid #e9ecef;
}

.api-key-item:last-child {
  margin-bottom: 0;
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.key-id {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

.key-status {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: uppercase;
}

.key-status.active {
  background: #d4edda;
  color: #155724;
}

.key-status.expired {
  background: #f8d7da;
  color: #721c24;
}

.key-details {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.key-details small {
  color: #6c757d;
  font-size: 11px;
}

.key-actions {
  display: flex;
  gap: 8px;
}

.no-keys {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px 0;
}

/* About Section */
.about-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.about-info p {
  margin-bottom: 8px;
}

.about-links {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.model-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.model-card .model-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.model-card h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.model-card p {
  font-size: 12px;
  color: #6c757d;
}

/* Buttons */
.btn {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: #007bff;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-danger {
  background: #dc3545;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-link {
  background: transparent;
  color: #007bff;
  border: none;
  padding: 8px 0;
  text-decoration: underline;
}

.btn-link:hover {
  background: transparent;
  color: #0056b3;
  transform: none;
}

/* Save Notification */
.save-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #28a745;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
}

.save-notification.show {
  transform: translateX(0);
  opacity: 1;
}

.save-icon {
  font-size: 16px;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast {
  background: #333;
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 350px;
  font-size: 13px;
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast-success {
  background: #28a745;
}

.toast-error {
  background: #dc3545;
}

.toast-info {
  background: #17a2b8;
}

.toast-warning {
  background: #ffc107;
  color: #212529;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    margin: 0;
    box-shadow: none;
  }
  
  .header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .header-actions {
    order: -1;
  }
  
  .nav-tabs {
    padding: 0 20px;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .main-content {
    padding: 20px;
  }
  
  .models-grid {
    grid-template-columns: 1fr;
  }
  
  .about-links {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .tab-btn {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .setting-group input,
  .setting-group select {
    max-width: 100%;
  }
  
  .key-details {
    flex-direction: column;
    gap: 4px;
  }
  
  .key-actions {
    flex-direction: column;
  }
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* Hidden utility */
.hidden {
  display: none !important;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}