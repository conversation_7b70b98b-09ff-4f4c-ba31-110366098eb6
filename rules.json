[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "set", "value": "*"}, {"header": "Access-Control-Allow-Methods", "operation": "set", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"header": "Access-Control-Allow-Headers", "operation": "set", "value": "Content-Type, Authorization, X-Requested-With"}]}, "condition": {"urlFilter": "localhost:*", "resourceTypes": ["xmlhttprequest", "main_frame"]}}]