#!/usr/bin/env node

/**
 * Quick start script for WebAI
 * This script helps users get the WebAI server running quickly
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 WebAI Quick Start');
console.log('==================');

// Check if we have the required dependencies
const packagePath = path.join(__dirname, 'package.json');
const serverPackagePath = path.join(__dirname, 'server-package.json');

if (!fs.existsSync(packagePath)) {
  if (fs.existsSync(serverPackagePath)) {
    console.log('📦 Copying server package.json...');
    fs.copyFileSync(serverPackagePath, packagePath);
  } else {
    console.error('❌ No package.json found. Please run from the webAI directory.');
    process.exit(1);
  }
}

// Check if node_modules exists
if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
  console.log('📥 Installing dependencies...');
  const npmInstall = spawn('npm', ['install'], { stdio: 'inherit' });
  
  npmInstall.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Dependencies installed successfully');
      startServer();
    } else {
      console.error('❌ Failed to install dependencies');
      process.exit(1);
    }
  });
} else {
  startServer();
}

function startServer() {
  const port = process.argv[2] || 3000;
  
  console.log(`\n🔧 Starting WebAI Server on port ${port}...`);
  console.log(`📡 HTTP Server: http://localhost:${port}`);
  console.log(`🔌 WebSocket: ws://localhost:${port + 1}`);
  console.log('\n💡 Instructions:');
  console.log('1. Install the WebAI browser extension');
  console.log('2. Click the extension icon and hit "Start Server"');
  console.log('3. Configure Cline with:');
  console.log(`   - Provider: OpenAI Compatible`);
  console.log(`   - Base URL: http://localhost:${port}`);
  console.log(`   - API Key: Generate from extension`);
  console.log('\n📊 Server Status:');
  
  const server = spawn('node', ['server.js', port], { stdio: 'inherit' });
  
  server.on('close', (code) => {
    if (code === 0) {
      console.log('\n✅ Server stopped gracefully');
    } else {
      console.log(`\n❌ Server exited with code ${code}`);
    }
  });
  
  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.kill('SIGINT');
  });
}