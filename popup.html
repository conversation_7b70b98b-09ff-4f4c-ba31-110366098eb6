<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAI - OpenAI Compatible Server</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="icons/icon32.png" alt="WebAI" class="logo-icon">
                <h1>WebAI</h1>
            </div>
            <div class="version">v1.0.0</div>
        </header>

        <main class="main-content">
            <!-- Server Status Section -->
            <section class="status-section">
                <div class="status-header">
                    <h2>Server Status</h2>
                    <div class="status-indicator" id="server-status">
                        <span class="status-dot offline"></span>
                        <span class="status-text">Offline</span>
                    </div>
                </div>
                
                <div class="server-info" id="server-info" style="display: none;">
                    <div class="info-row">
                        <span class="label">URL:</span>
                        <span class="value" id="server-url">http://localhost:3000</span>
                        <button class="copy-btn" id="copy-url-btn" title="Copy URL">📋</button>
                    </div>
                    <div class="info-row">
                        <span class="label">Port:</span>
                        <span class="value" id="server-port">3000</span>
                    </div>
                </div>

                <div class="server-controls">
                    <button class="btn btn-primary" id="start-server-btn">Start Server</button>
                    <button class="btn btn-secondary" id="stop-server-btn" style="display: none;">Stop Server</button>
                </div>
            </section>

            <!-- API Key Section -->
            <section class="api-key-section">
                <div class="section-header">
                    <h2>API Key</h2>
                    <button class="btn btn-small" id="generate-key-btn">Generate New</button>
                </div>
                
                <div class="api-key-display" id="api-key-display" style="display: none;">
                    <div class="key-container">
                        <input type="password" id="api-key-input" readonly class="key-input">
                        <button class="btn btn-icon" id="toggle-key-visibility" title="Show/Hide Key">👁️</button>
                        <button class="btn btn-icon" id="copy-key-btn" title="Copy Key">📋</button>
                    </div>
                    <div class="key-info">
                        <small class="key-expires" id="key-expires">Expires: Never</small>
                        <button class="btn btn-small btn-danger" id="revoke-key-btn">Revoke</button>
                    </div>
                </div>

                <div class="no-key-message" id="no-key-message">
                    <p>No API key generated. Click "Generate New" to create one.</p>
                </div>
            </section>

            <!-- Quick Settings -->
            <section class="quick-settings">
                <h2>Quick Settings</h2>
                
                <div class="setting-row">
                    <label for="default-model">Default Model:</label>
                    <select id="default-model" class="setting-select">
                        <option value="claude-web">Claude (Web)</option>
                        <option value="gemini-web">Gemini (Web)</option>
                        <option value="bing-copilot">Bing Copilot</option>
                        <option value="deepseek-web">DeepSeek (Web)</option>
                        <option value="perplexity-web">Perplexity (Web)</option>
                    </select>
                </div>

                <div class="setting-row">
                    <label for="auto-start">Auto-start server:</label>
                    <input type="checkbox" id="auto-start" class="setting-checkbox">
                </div>

                <div class="setting-row">
                    <label for="port-input">Server Port:</label>
                    <input type="number" id="port-input" min="3000" max="9999" value="3000" class="setting-input">
                </div>
            </section>

            <!-- Usage Instructions -->
            <section class="usage-section">
                <h2>Usage in Cline</h2>
                <div class="instruction-steps">
                    <div class="step">
                        <span class="step-number">1</span>
                        <span class="step-text">Start the server above</span>
                    </div>
                    <div class="step">
                        <span class="step-number">2</span>
                        <span class="step-text">Copy the server URL and API key</span>
                    </div>
                    <div class="step">
                        <span class="step-number">3</span>
                        <span class="step-text">In Cline, select "OpenAI Compatible"</span>
                    </div>
                    <div class="step">
                        <span class="step-number">4</span>
                        <span class="step-text">Paste URL and API key, choose model</span>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <button class="btn btn-link" id="open-settings-btn">Advanced Settings</button>
            <button class="btn btn-link" id="open-docs-btn">Documentation</button>
        </footer>

        <!-- Toast notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <script src="dist/popup.bundle.js"></script>
</body>
</html>