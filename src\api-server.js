/**
 * API Server implementation for WebAI
 * This module handles the OpenAI-compatible API endpoints
 * Note: Since browser extensions can't create actual HTTP servers,
 * this uses message passing and request interception
 */
import browser from 'webextension-polyfill';

class WebAIApiServer {
  constructor() {
    this.isRunning = false;
    this.port = 3000;
    this.activeConnections = new Map();
    this.requestHandlers = new Map();
  }

  async start(port = 3000) {
    if (this.isRunning) {
      throw new Error('Server already running');
    }

    this.port = port;
    this.isRunning = true;

    // Set up request interception for localhost
    this.setupRequestInterception();
    
    console.log(`WebAI API Server started on port ${port}`);
    return {
      port: this.port,
      url: `http://localhost:${this.port}`
    };
  }

  async stop() {
    if (!this.isRunning) {
      return;
    }

    // Clean up active connections
    for (const [requestId, handler] of this.requestHandlers) {
      if (handler.controller) {
        handler.controller.abort();
      }
    }
    
    this.requestHandlers.clear();
    this.activeConnections.clear();
    this.isRunning = false;
    
    console.log('WebAI API Server stopped');
  }

  setupRequestInterception() {
    // Use declarativeNetRequest to modify headers for CORS
    // The actual request handling is done in background.js via message passing
    
    // Listen for API requests via content script injection
    this.injectContentScript();
  }

  async injectContentScript() {
    // Inject a content script that will intercept fetch requests to localhost
    const script = `
      (function() {
        const originalFetch = window.fetch;
        
        window.fetch = async function(url, options = {}) {
          const urlObj = new URL(url, window.location.origin);
          
          // Check if this is a request to our API server
          if (urlObj.hostname === 'localhost' && urlObj.port === '${this.port}') {
            try {
              // Send request to extension background script
              const response = await new Promise((resolve, reject) => {
                const requestData = {
                  url: urlObj.toString(),
                  method: options.method || 'GET',
                  headers: options.headers || {},
                  body: options.body || null
                };
                
                window.postMessage({
                  type: 'WEBAI_API_REQUEST',
                  requestData
                }, '*');
                
                // Listen for response
                const handleResponse = (event) => {
                  if (event.data.type === 'WEBAI_API_RESPONSE') {
                    window.removeEventListener('message', handleResponse);
                    resolve(event.data.response);
                  }
                };
                
                window.addEventListener('message', handleResponse);
                
                // Timeout after 30 seconds
                setTimeout(() => {
                  window.removeEventListener('message', handleResponse);
                  reject(new Error('Request timeout'));
                }, 30000);
              });
              
              // Create a Response object
              return new Response(response.body, {
                status: response.status,
                statusText: response.statusText || 'OK',
                headers: response.headers
              });
              
            } catch (error) {
              throw new Error(\`WebAI API Error: \${error.message}\`);
            }
          }
          
          // For non-API requests, use original fetch
          return originalFetch.call(this, url, options);
        };
        
        // Listen for messages from content script
        window.addEventListener('message', async (event) => {
          if (event.data.type === 'WEBAI_API_REQUEST') {
            try {
              const response = await chrome.runtime.sendMessage({
                type: 'API_REQUEST',
                request: event.data.requestData
              });
              
              window.postMessage({
                type: 'WEBAI_API_RESPONSE',
                response: response.response
              }, '*');
              
            } catch (error) {
              window.postMessage({
                type: 'WEBAI_API_RESPONSE',
                response: {
                  status: 500,
                  body: JSON.stringify({ error: { message: error.message } })
                }
              }, '*');
            }
          }
        });
        
        console.log('WebAI API client interceptor loaded');
      })();
    `;

    // Inject the script into all tabs
    try {
      const tabs = await browser.tabs.query({});
      for (const tab of tabs) {
        try {
          await browser.scripting.executeScript({
            target: { tabId: tab.id },
            code: script
          });
        } catch (error) {
          // Ignore errors for special pages that can't be scripted
          console.debug(`Could not inject script into tab ${tab.id}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to inject content scripts:', error);
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      port: this.port,
      url: this.isRunning ? `http://localhost:${this.port}` : null,
      activeConnections: this.activeConnections.size,
      activeRequests: this.requestHandlers.size
    };
  }
}

// Export singleton instance
export const apiServer = new WebAIApiServer();

// Auto-start server if configured
browser.storage.local.get(['autoStart']).then(result => {
  if (result.autoStart) {
    apiServer.start().catch(console.error);
  }
});

export default apiServer;