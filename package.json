{"name": "webai-openai-compatible-server", "version": "1.0.0", "description": "OpenAI-compatible local server for various AI models via browser extension", "main": "background.js", "scripts": {"build": "webpack", "dev": "webpack --mode development", "watch": "webpack --mode development --watch", "package": "npm run build && zip -r webai-extension.zip . -x node_modules/\\* .git/\\* src/\\* *.log", "clean": "rimraf dist/ webai-extension.zip", "copy-lib": "node scripts/copy-lib.js"}, "keywords": ["ai", "openai", "api", "claude", "gemini", "browser-extension", "local-server"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"copy-webpack-plugin": "^12.0.2", "rimraf": "^5.0.5", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "dependencies": {"uuid": "^9.0.0", "webextension-polyfill": "^0.12.0", "ws": "^8.18.2"}}