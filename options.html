<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAI Settings</title>
    <link rel="stylesheet" href="styles/options.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="icons/icon48.png" alt="WebAI" class="logo-icon">
                <h1>WebAI Settings</h1>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" id="export-settings-btn">Export Settings</button>
                <button class="btn btn-secondary" id="import-settings-btn">Import Settings</button>
                <input type="file" id="import-file-input" accept=".json" style="display: none;">
            </div>
        </header>

        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="general">General</button>
            <button class="tab-btn" data-tab="models">Models</button>
            <button class="tab-btn" data-tab="security">Security</button>
            <button class="tab-btn" data-tab="advanced">Advanced</button>
            <button class="tab-btn" data-tab="about">About</button>
        </nav>

        <main class="main-content">
            <!-- General Settings Tab -->
            <div class="tab-content active" id="general-tab">
                <section class="settings-section">
                    <h2>Server Configuration</h2>
                    
                    <div class="setting-group">
                        <label for="server-port">Server Port</label>
                        <input type="number" id="server-port" min="3000" max="9999" value="3000">
                        <small class="help-text">Port for the local API server (requires restart)</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-start-server">
                            <span class="checkmark"></span>
                            Auto-start server on extension load
                        </label>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="cors-enabled" checked>
                            <span class="checkmark"></span>
                            Enable CORS (required for web apps)
                        </label>
                    </div>
                </section>

                <section class="settings-section">
                    <h2>Default Model Settings</h2>
                    
                    <div class="setting-group">
                        <label for="default-model">Default Model</label>
                        <select id="default-model">
                            <option value="claude-web">Claude (Web)</option>
                            <option value="gemini-web">Gemini (Web)</option>
                            <option value="bing-copilot">Bing Copilot</option>
                            <option value="deepseek-web">DeepSeek (Web)</option>
                            <option value="perplexity-web">Perplexity (Web)</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label for="default-temperature">Default Temperature</label>
                        <input type="range" id="default-temperature" min="0" max="2" step="0.1" value="1.0">
                        <span class="range-value" id="temperature-value">1.0</span>
                        <small class="help-text">Controls randomness in responses (0 = deterministic, 2 = very random)</small>
                    </div>

                    <div class="setting-group">
                        <label for="default-max-tokens">Default Max Tokens</label>
                        <input type="number" id="default-max-tokens" min="1" max="4096" value="2048">
                        <small class="help-text">Maximum number of tokens in response</small>
                    </div>
                </section>
            </div>

            <!-- Models Tab -->
            <div class="tab-content" id="models-tab">
                <section class="settings-section">
                    <h2>Model Configuration</h2>
                    
                    <!-- Claude Settings -->
                    <div class="model-config" data-model="claude-web">
                        <h3>
                            <span class="model-icon">🤖</span>
                            Claude (Web)
                            <span class="model-status" id="claude-status">Not Connected</span>
                        </h3>
                        
                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="claude-enabled" checked>
                                <span class="checkmark"></span>
                                Enable Claude model
                            </label>
                        </div>

                        <div class="setting-group">
                            <label for="claude-style">Response Style</label>
                            <select id="claude-style">
                                <option value="default">Default</option>
                                <option value="creative">Creative</option>
                                <option value="balanced">Balanced</option>
                                <option value="precise">Precise</option>
                            </select>
                        </div>

                        <button class="btn btn-secondary" id="test-claude-btn">Test Connection</button>
                    </div>

                    <!-- Gemini Settings -->
                    <div class="model-config" data-model="gemini-web">
                        <h3>
                            <span class="model-icon">💎</span>
                            Gemini (Web)
                            <span class="model-status" id="gemini-status">Not Connected</span>
                        </h3>
                        
                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="gemini-enabled" checked>
                                <span class="checkmark"></span>
                                Enable Gemini model
                            </label>
                        </div>

                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="gemini-image-support" checked>
                                <span class="checkmark"></span>
                                Enable image support
                            </label>
                        </div>

                        <button class="btn btn-secondary" id="test-gemini-btn">Test Connection</button>
                    </div>

                    <!-- Bing Copilot Settings -->
                    <div class="model-config" data-model="bing-copilot">
                        <h3>
                            <span class="model-icon">🔵</span>
                            Bing Copilot
                            <span class="model-status" id="bing-status">Not Connected</span>
                        </h3>
                        
                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="bing-enabled" checked>
                                <span class="checkmark"></span>
                                Enable Bing Copilot model
                            </label>
                        </div>

                        <div class="setting-group">
                            <label for="bing-mode">Default Mode</label>
                            <select id="bing-mode">
                                <option value="chat">Chat</option>
                                <option value="reasoning">Reasoning</option>
                            </select>
                        </div>

                        <button class="btn btn-secondary" id="test-bing-btn">Test Connection</button>
                    </div>

                    <!-- DeepSeek Settings -->
                    <div class="model-config" data-model="deepseek-web">
                        <h3>
                            <span class="model-icon">🧠</span>
                            DeepSeek (Web)
                            <span class="model-status" id="deepseek-status">Not Connected</span>
                        </h3>
                        
                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="deepseek-enabled" checked>
                                <span class="checkmark"></span>
                                Enable DeepSeek model
                            </label>
                        </div>

                        <div class="setting-group">
                            <label for="deepseek-mode">Default Mode</label>
                            <select id="deepseek-mode">
                                <option value="chat">Chat</option>
                                <option value="reasoning">Reasoning</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="deepseek-search" checked>
                                <span class="checkmark"></span>
                                Enable web search by default
                            </label>
                        </div>

                        <button class="btn btn-secondary" id="test-deepseek-btn">Test Connection</button>
                    </div>

                    <!-- Perplexity Settings -->
                    <div class="model-config" data-model="perplexity-web">
                        <h3>
                            <span class="model-icon">🔍</span>
                            Perplexity (Web)
                            <span class="model-status" id="perplexity-status">Not Connected</span>
                        </h3>
                        
                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="perplexity-enabled" checked>
                                <span class="checkmark"></span>
                                Enable Perplexity model
                            </label>
                        </div>

                        <div class="setting-group">
                            <label for="perplexity-focus">Default Focus</label>
                            <select id="perplexity-focus">
                                <option value="internet">Internet</option>
                                <option value="writing">Writing</option>
                                <option value="academic">Academic</option>
                            </select>
                        </div>

                        <button class="btn btn-secondary" id="test-perplexity-btn">Test Connection</button>
                    </div>
                </section>
            </div>

            <!-- Security Tab -->
            <div class="tab-content" id="security-tab">
                <section class="settings-section">
                    <h2>API Key Management</h2>
                    
                    <div class="api-keys-list" id="api-keys-list">
                        <!-- API keys will be populated here -->
                    </div>

                    <div class="setting-group">
                        <label for="key-expiry">Default Key Expiry</label>
                        <select id="key-expiry">
                            <option value="1h">1 Hour</option>
                            <option value="24h" selected>24 Hours</option>
                            <option value="7d">7 Days</option>
                            <option value="30d">30 Days</option>
                            <option value="never">Never</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="require-auth" checked>
                            <span class="checkmark"></span>
                            Require API key for all requests
                        </label>
                    </div>

                    <div class="setting-group">
                        <button class="btn btn-primary" id="generate-new-key-btn">Generate New API Key</button>
                        <button class="btn btn-danger" id="revoke-all-keys-btn">Revoke All Keys</button>
                    </div>
                </section>

                <section class="settings-section">
                    <h2>Security Settings</h2>
                    
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="localhost-only" checked>
                            <span class="checkmark"></span>
                            Only accept connections from localhost
                        </label>
                        <small class="help-text">Recommended for security</small>
                    </div>

                    <div class="setting-group">
                        <label for="rate-limit">Rate Limit (requests per minute)</label>
                        <input type="number" id="rate-limit" min="1" max="1000" value="60">
                    </div>
                </section>
            </div>

            <!-- Advanced Tab -->
            <div class="tab-content" id="advanced-tab">
                <section class="settings-section">
                    <h2>Debugging</h2>
                    
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="debug-mode">
                            <span class="checkmark"></span>
                            Enable debug mode
                        </label>
                        <small class="help-text">Shows detailed logs in browser console</small>
                    </div>

                    <div class="setting-group">
                        <label for="log-level">Log Level</label>
                        <select id="log-level">
                            <option value="error">Error</option>
                            <option value="warn">Warning</option>
                            <option value="info" selected>Info</option>
                            <option value="debug">Debug</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <button class="btn btn-secondary" id="clear-logs-btn">Clear Logs</button>
                        <button class="btn btn-secondary" id="export-logs-btn">Export Logs</button>
                    </div>
                </section>

                <section class="settings-section">
                    <h2>Data Management</h2>
                    
                    <div class="setting-group">
                        <button class="btn btn-secondary" id="clear-cache-btn">Clear Cache</button>
                        <button class="btn btn-danger" id="reset-settings-btn">Reset All Settings</button>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-cleanup">
                            <span class="checkmark"></span>
                            Automatically cleanup old conversations
                        </label>
                    </div>
                </section>
            </div>

            <!-- About Tab -->
            <div class="tab-content" id="about-tab">
                <section class="settings-section">
                    <h2>About WebAI</h2>
                    
                    <div class="about-info">
                        <p><strong>Version:</strong> 1.0.0</p>
                        <p><strong>Author:</strong> Sukarth Acharya</p>
                        <p><strong>License:</strong> MIT</p>
                        <p><strong>Description:</strong> OpenAI-compatible local server for various AI models</p>
                    </div>

                    <div class="about-links">
                        <a href="#" class="btn btn-link" id="github-link">GitHub Repository</a>
                        <a href="#" class="btn btn-link" id="docs-link">Documentation</a>
                        <a href="#" class="btn btn-link" id="support-link">Support</a>
                    </div>
                </section>

                <section class="settings-section">
                    <h2>Supported Models</h2>
                    
                    <div class="models-grid">
                        <div class="model-card">
                            <div class="model-icon">🤖</div>
                            <h4>Claude</h4>
                            <p>Anthropic's AI assistant</p>
                        </div>
                        <div class="model-card">
                            <div class="model-icon">💎</div>
                            <h4>Gemini</h4>
                            <p>Google's multimodal AI</p>
                        </div>
                        <div class="model-card">
                            <div class="model-icon">🔵</div>
                            <h4>Bing Copilot</h4>
                            <p>Microsoft's AI assistant</p>
                        </div>
                        <div class="model-card">
                            <div class="model-icon">🧠</div>
                            <h4>DeepSeek</h4>
                            <p>Advanced reasoning AI</p>
                        </div>
                        <div class="model-card">
                            <div class="model-icon">🔍</div>
                            <h4>Perplexity</h4>
                            <p>AI-powered search</p>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Save notification -->
        <div class="save-notification" id="save-notification">
            <span class="save-icon">✓</span>
            <span class="save-text">Settings saved</span>
        </div>

        <!-- Toast notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <script src="dist/options.bundle.js"></script>
</body>
</html>