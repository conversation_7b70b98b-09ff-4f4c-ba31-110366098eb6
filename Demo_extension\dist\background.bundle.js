(()=>{var e={815:function(e,t){var r,a;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,r=function(e){"use strict";if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw new Error("This script should only be loaded in a browser extension.");if(globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id)e.exports=globalThis.browser;else{const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class a extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const s=(t,r)=>(...a)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||a.length<=1&&!1!==r.singleCallbackArg?t.resolve(a[0]):t.resolve(a)},n=e=>1==e?"argument":"arguments",o=(e,t,r)=>new Proxy(t,{apply:(t,a,s)=>r.call(a,e,...s)});let i=Function.call.bind(Object.prototype.hasOwnProperty);const d=(e,t={},r={})=>{let a=Object.create(null),l={has:(t,r)=>r in e||r in a,get(l,c,g){if(c in a)return a[c];if(!(c in e))return;let h=e[c];if("function"==typeof h)if("function"==typeof t[c])h=o(e,e[c],t[c]);else if(i(r,c)){let t=((e,t)=>function(r,...a){if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise(((n,o)=>{if(t.fallbackToNoCallback)try{r[e](...a,s({resolve:n,reject:o},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...a),t.fallbackToNoCallback=!1,t.noCallback=!0,n()}else t.noCallback?(r[e](...a),n()):r[e](...a,s({resolve:n,reject:o},t))}))})(c,r[c]);h=o(e,e[c],t)}else h=h.bind(e);else if("object"==typeof h&&null!==h&&(i(t,c)||i(r,c)))h=d(h,t[c],r[c]);else{if(!i(r,"*"))return Object.defineProperty(a,c,{configurable:!0,enumerable:!0,get:()=>e[c],set(t){e[c]=t}}),h;h=d(h,t[c],r["*"])}return a[c]=h,h},set:(t,r,s,n)=>(r in a?a[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(a,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(a,t)},c=Object.create(e);return new Proxy(c,l)},l=e=>({addListener(t,r,...a){t.addListener(e.get(r),...a)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),c=new a((e=>"function"!=typeof e?e:function(t){const r=d(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),g=new a((e=>"function"!=typeof e?e:function(t,r,a){let s,n,o=!1,i=new Promise((e=>{s=function(t){o=!0,e(t)}}));try{n=e(t,r,s)}catch(e){n=Promise.reject(e)}const d=!0!==n&&((l=n)&&"object"==typeof l&&"function"==typeof l.then);var l;if(!0!==n&&!d&&!o)return!1;return(d?n:i).then((e=>{a(e)}),(e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",a({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error("Failed to send onMessage rejected reply",e)})),!0})),h=({reject:r,resolve:a},s)=>{e.runtime.lastError?e.runtime.lastError.message===t?a():r(new Error(e.runtime.lastError.message)):s&&s.__mozWebExtensionPolyfillReject__?r(new Error(s.message)):a(s)},u=(e,t,r,...a)=>{if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise(((e,t)=>{const s=h.bind(null,{resolve:e,reject:t});a.push(s),r.sendMessage(...a)}))},m={devtools:{network:{onRequestFinished:l(c)}},runtime:{onMessage:l(g),onMessageExternal:l(g),sendMessage:u.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:u.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},p={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":p},services:{"*":p},websites:{"*":p}},d(e,m,r)};e.exports=r(chrome)}},void 0===(a=r.apply(t,[e]))||(e.exports=a)}},t={};function r(a){var s=t[a];if(void 0!==s)return s.exports;var n=t[a]={exports:{}};return e[a].call(n.exports,n,n.exports,r),n.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e,t=r(815),a=r.n(t);!function(e){e.UNKNOWN_ERROR="unknown_error",e.NETWORK_ERROR="network_error",e.UNAUTHORIZED="unauthorized",e.SERVICE_UNAVAILABLE="service_unavailable",e.MISSING_API_KEY="missing_api_key",e.MISSING_HOST_PERMISSION="missing_host_permission",e.CONVERSATION_LIMIT="conversation_limit",e.CONTENT_FILTERED="content_filtered",e.INVALID_REQUEST="invalid_request",e.INVALID_API_KEY="invalid_api_key",e.INVALID_THREAD_ID="invalid_thread_id",e.INVALID_METADATA="invalid_metadata",e.INVALID_MESSAGE_ID="invalid_message_id",e.INVALID_MODEL="invalid_model",e.INVALID_IMAGE_TYPE="invalid_image_type",e.INVALID_IMAGE_CONTENT="invalid_image_content",e.UPLOAD_FAILED="upload_failed",e.UPLOAD_TIMEOUT="upload_timeout",e.UPLOAD_SIZE_EXCEEDED="upload_size_exceeded",e.UPLOAD_TYPE_EXCEEDED="upload_type_exceeded",e.UPLOAD_AMOUNT_EXCEEDED="upload_amount_exceeded",e.UPLOAD_TYPE_NOT_SUPPORTED="upload_type_not_supported",e.RATE_LIMIT_EXCEEDED="rate_limit_exceeded",e.METADATA_INITIALIZATION_ERROR="metadata_initialization_error",e.FEATURE_NOT_SUPPORTED="feature_not_supported",e.RESPONSE_PARSING_ERROR="response_parsing_error",e.POW_CHALLENGE_FAILED="pow_challenge_failed"}(e||(e={}));class s extends Error{constructor(t,r=e.UNKNOWN_ERROR){super(t),this.code=r,this.name="AIModelError"}}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self&&self;var n={exports:{}};"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,function(e){if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(void 0===globalThis.browser||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class a extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const s=(t,r)=>(...a)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||a.length<=1&&!1!==r.singleCallbackArg?t.resolve(a[0]):t.resolve(a)},n=e=>1==e?"argument":"arguments",o=(e,t)=>function(r,...a){if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise(((n,o)=>{if(t.fallbackToNoCallback)try{r[e](...a,s({resolve:n,reject:o},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...a),t.fallbackToNoCallback=!1,t.noCallback=!0,n()}else t.noCallback?(r[e](...a),n()):r[e](...a,s({resolve:n,reject:o},t))}))},i=(e,t,r)=>new Proxy(t,{apply:(t,a,s)=>r.call(a,e,...s)});let d=Function.call.bind(Object.prototype.hasOwnProperty);const l=(e,t={},r={})=>{let a=Object.create(null),s={has:(t,r)=>r in e||r in a,get(s,n,c){if(n in a)return a[n];if(!(n in e))return;let g=e[n];if("function"==typeof g)if("function"==typeof t[n])g=i(e,e[n],t[n]);else if(d(r,n)){let t=o(n,r[n]);g=i(e,e[n],t)}else g=g.bind(e);else if("object"==typeof g&&null!==g&&(d(t,n)||d(r,n)))g=l(g,t[n],r[n]);else{if(!d(r,"*"))return Object.defineProperty(a,n,{configurable:!0,enumerable:!0,get:()=>e[n],set(t){e[n]=t}}),g;g=l(g,t[n],r["*"])}return a[n]=g,g},set:(t,r,s,n)=>(r in a?a[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(a,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(a,t)},n=Object.create(e);return new Proxy(n,s)},c=e=>({addListener(t,r,...a){t.addListener(e.get(r),...a)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),g=new a((e=>"function"!=typeof e?e:function(t){const r=l(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),h=new a((e=>"function"!=typeof e?e:function(t,r,a){let s,n,o=!1,i=new Promise((e=>{s=function(t){o=!0,e(t)}}));try{n=e(t,r,s)}catch(e){n=Promise.reject(e)}const d=!0!==n&&(e=>e&&"object"==typeof e&&"function"==typeof e.then)(n);return!(!0!==n&&!d&&!o)&&((e=>{e.then((e=>{a(e)}),(e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",a({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error("Failed to send onMessage rejected reply",e)}))})(d?n:i),!0)})),u=({reject:r,resolve:a},s)=>{e.runtime.lastError?e.runtime.lastError.message===t?a():r(new Error(e.runtime.lastError.message)):s&&s.__mozWebExtensionPolyfillReject__?r(new Error(s.message)):a(s)},m=(e,t,r,...a)=>{if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise(((e,t)=>{const s=u.bind(null,{resolve:e,reject:t});a.push(s),r.sendMessage(...a)}))},p={devtools:{network:{onRequestFinished:c(g)}},runtime:{onMessage:c(h),onMessageExternal:c(h),sendMessage:m.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:m.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},A={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":A},services:{"*":A},websites:{"*":A}},l(e,p,r)};e.exports=r(chrome)}else e.exports=globalThis.browser}(n);var o=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(n.exports);let i;const d=new Uint8Array(16);function l(){if(!i&&(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!i))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(d)}const c=[];for(let e=0;e<256;++e)c.push((e+256).toString(16).slice(1));var g={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function h(e,t,r){if(g.randomUUID&&!e)return g.randomUUID();const a=(e=e||{}).random||(e.rng||l)();return a[6]=15&a[6]|64,a[8]=63&a[8]|128,function(e,t=0){return c[e[t+0]]+c[e[t+1]]+c[e[t+2]]+c[e[t+3]]+"-"+c[e[t+4]]+c[e[t+5]]+"-"+c[e[t+6]]+c[e[t+7]]+"-"+c[e[t+8]]+c[e[t+9]]+"-"+c[e[t+10]]+c[e[t+11]]+c[e[t+12]]+c[e[t+13]]+c[e[t+14]]+c[e[t+15]]}(a)}class u{constructor(){this.baseUrl="",this.defaultModel=""}async sendMessage(t,r={onEvent:()=>{}}){try{let e="";return await this.doSendMessage({prompt:t,images:r.images,signal:r.signal,mode:r.mode,model:r.model,style_key:r.style_key,searchFocus:r.searchFocus,searchSources:r.searchSources,searchEnabled:r.searchEnabled,onEvent:t=>{"UPDATE_ANSWER"===t.type&&(e=t.data.text,r.onEvent({type:"UPDATE_ANSWER",data:t.data})),"DONE"===t.type&&r.onEvent({type:"DONE",data:t.data}),"SUGGESTED_RESPONSES"===t.type&&r.onEvent({type:"SUGGESTED_RESPONSES",data:t.data}),"TITLE_UPDATE"===t.type&&r.onEvent({type:"TITLE_UPDATE",data:t.data}),"ERROR"===t.type&&r.onEvent({type:"ERROR",error:t.error})}}),e}catch(t){r.onEvent({type:"ERROR",error:t instanceof s?t:new s(t instanceof Error?t.message:String(t),t instanceof s?t.code:e.UNKNOWN_ERROR)}),this.handleModelError("Error sending message",t instanceof s?t.code:void 0,r,t)}}async getAllThreads(){try{return(await o.storage.local.get(u.THREADS_STORAGE_KEY))[u.THREADS_STORAGE_KEY]||[]}catch(e){return console.error("Failed to load threads from storage:",e),[]}}async saveThreadsToStorage(e){try{await o.storage.local.set({[u.THREADS_STORAGE_KEY]:e})}catch(e){console.error("Failed to save threads to storage:",e)}}getCurrentThread(){return this.currentThread}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r)return this.handleModelError("Thread not found",e.INVALID_THREAD_ID);this.currentThread=r}async saveThread(){if(!this.currentThread)return this.handleModelError("No active thread to save",e.INVALID_REQUEST);const t=await this.getAllThreads(),r=t.findIndex((e=>e.id===this.currentThread.id));-1!==r?t[r]=this.currentThread:t.push(this.currentThread),await this.saveThreadsToStorage(t)}async deleteThread(e,t=!0){const r=await this.getAllThreads();await this.saveThreadsToStorage(r.filter((t=>t.id!==e))),this.currentThread?.id===e&&t&&this.initNewThread()}createMessage(e,t){return{id:h(),role:e,content:t,timestamp:Date.now()}}getBaseUrl(){return this.baseUrl}handleModelError(t,r,a,n){!r&&n instanceof s?r=n.code:r||(n instanceof Error&&("AbortError"===n.name?r=e.RATE_LIMIT_EXCEEDED:n.message.includes("network")||n.message.includes("connection")?r=e.NETWORK_ERROR:n.message.includes("permission")||n.message.includes("unauthorized")?r=e.UNAUTHORIZED:n.message.includes("timeout")&&(r=e.SERVICE_UNAVAILABLE)),r=r||e.UNKNOWN_ERROR);const o=n?n instanceof Error?n.message:String(n):"",i=new s(o?`${o} - ${t}`:t,r);throw n&&"cause"in Error&&Object.assign(i,{cause:n}),a?.onEvent&&a.onEvent({type:"ERROR",error:i}),console.error("AI model error:",i),i}async shareConversation(t){return this.handleModelError(`Sharing is not supported by the ${this.getName()} model`,e.FEATURE_NOT_SUPPORTED)}}async function m(e,t,r,a,s=!1){console.log(`[${e} Auth Wrapper] Requesting token from background (forceRefresh: ${s})...`);try{const n=await o.runtime.sendMessage({type:"GET_AUTH_TOKEN_FROM_WEBSITE",payload:{serviceName:e,targetUrl:t,urlPattern:r,extractorName:a,forceNewTab:s}});if(console.log(`[${e} Auth Wrapper] Received response from background:`,n),n?.success)return n.token||null;{const t=n?.error||`Unknown error from background script for ${e}`;throw console.error(`[${e} Auth Wrapper] Background script failed: ${t}`),new Error(t)}}catch(t){if(console.error(`[${e} Auth Wrapper] Error communicating with background script:`,t),t instanceof Error&&(t.message.includes("Could not establish connection")||t.message.includes("Receiving end does not exist"))){const e="Background service communication error. Is the extension enabled/reloaded? Check background script logs.";throw console.error(e),new Error(e)}throw t}}function p(e,t,r,a){var s,n=arguments.length,o=n<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,r):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,r,a);else for(var i=e.length-1;i>=0;i--)(s=e[i])&&(o=(n<3?s(o):n>3?s(t,r,o):s(t,r))||o);return n>3&&o&&Object.defineProperty(t,r,o),o}function A(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}u.THREADS_STORAGE_KEY="chat_threads","function"==typeof SuppressedError&&SuppressedError;const f=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,E=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,y=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function w(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t;!function(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}(e)}function _(e,t={}){if("string"!=typeof e)return e;const r=e.trim();if('"'===e[0]&&e.endsWith('"')&&!e.includes("\\"))return r.slice(1,-1);if(r.length<=9){const e=r.toLowerCase();if("true"===e)return!0;if("false"===e)return!1;if("undefined"===e)return;if("null"===e)return null;if("nan"===e)return Number.NaN;if("infinity"===e)return Number.POSITIVE_INFINITY;if("-infinity"===e)return Number.NEGATIVE_INFINITY}if(!y.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(f.test(e)||E.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,w)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}const T=/#/g,v=/&/g,S=/\//g,b=/=/g,x=/\+/g,I=/%5e/gi,R=/%60/gi,N=/%7c/gi,k=/%20/gi;function M(e){return(t="string"==typeof e?e:JSON.stringify(e),encodeURI(""+t).replace(N,"|")).replace(x,"%2B").replace(k,"+").replace(T,"%23").replace(v,"%26").replace(R,"`").replace(I,"^").replace(S,"%2F");var t}function D(e){return M(e).replace(b,"%3D")}function U(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function P(e){return U(e.replace(x," "))}function L(e=""){const t={};"?"===e[0]&&(e=e.slice(1));for(const r of e.split("&")){const e=r.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const a=U(e[1].replace(x," "));if("__proto__"===a||"constructor"===a)continue;const s=P(e[2]||"");void 0===t[a]?t[a]=s:Array.isArray(t[a])?t[a].push(s):t[a]=[t[a],s]}return t}const O=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,C=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,$=/^([/\\]\s*){2,}[^/\\]/,V=/^\.?\//;function F(e,t={}){return"boolean"==typeof t&&(t={acceptRelative:t}),t.strict?O.test(e):C.test(e)||!!t.acceptRelative&&$.test(e)}function B(e="",t){return e.endsWith("/")?e:e+"/"}const j=Symbol.for("ufo:protocolRelative");function z(e=""){const[t="",r="",a=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:r,hash:a}}class q extends Error{constructor(e,t){super(e,t),this.name="FetchError",t?.cause&&!this.cause&&(this.cause=t.cause)}}const W=new Set(Object.freeze(["PATCH","POST","PUT","DELETE"]));function G(e="GET"){return W.has(e.toUpperCase())}const H=new Set(["image/svg","application/xml","application/xhtml","application/html"]),J=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function K(e,t,r,a){const s=function(e,t,r){if(!t)return new r(e);const a=new r(t);if(e)for(const[t,s]of Symbol.iterator in e||Array.isArray(e)?e:new r(e))a.set(t,s);return a}(t?.headers??e?.headers,r?.headers,a);let n;return(r?.query||r?.params||t?.params||t?.query)&&(n={...r?.params,...r?.query,...t?.params,...t?.query}),{...r,...t,query:n,params:n,headers:s}}async function Q(e,t){if(t)if(Array.isArray(t))for(const r of t)await r(e);else await t(e)}const Z=new Set([408,409,425,429,500,502,503,504]),X=new Set([101,204,205,304]),Y=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw new Error("unable to locate global object")}(),ee=function e(t={}){const{fetch:r=globalThis.fetch,Headers:a=globalThis.Headers,AbortController:s=globalThis.AbortController}=t;async function n(e){const t=e.error&&"AbortError"===e.error.name&&!e.options.timeout||!1;if(!1!==e.options.retry&&!t){let t;t="number"==typeof e.options.retry?e.options.retry:G(e.options.method)?0:1;const r=e.response&&e.response.status||500;if(t>0&&(Array.isArray(e.options.retryStatusCodes)?e.options.retryStatusCodes.includes(r):Z.has(r))){const r="function"==typeof e.options.retryDelay?e.options.retryDelay(e):e.options.retryDelay||0;return r>0&&await new Promise((e=>setTimeout(e,r))),o(e.request,{...e.options,retry:t-1})}}const r=function(e){const t=e.error?.message||e.error?.toString()||"",r=e.request?.method||e.options?.method||"GET",a=e.request?.url||String(e.request)||"/",s=`[${r}] ${JSON.stringify(a)}`,n=e.response?`${e.response.status} ${e.response.statusText}`:"<no response>",o=new q(`${s}: ${n}${t?` ${t}`:""}`,e.error?{cause:e.error}:void 0);for(const t of["request","options","response"])Object.defineProperty(o,t,{get:()=>e[t]});for(const[t,r]of[["data","_data"],["status","status"],["statusCode","status"],["statusText","statusText"],["statusMessage","statusText"]])Object.defineProperty(o,t,{get:()=>e.response&&e.response[r]});return o}(e);throw Error.captureStackTrace&&Error.captureStackTrace(r,o),r}const o=async function(e,o={}){const i={request:e,options:K(e,o,t.defaults,a),response:void 0,error:void 0};let d;if(i.options.method&&(i.options.method=i.options.method.toUpperCase()),i.options.onRequest&&await Q(i,i.options.onRequest),"string"==typeof i.request&&(i.options.baseURL&&(i.request=function(e,t){if(!(r=t)||"/"===r||F(e))return e;var r;const a=function(e=""){return(function(e=""){return e.endsWith("/")}(e)?e.slice(0,-1):e)||"/"}(t);return e.startsWith(a)?e:function(e,...t){let r=e||"";for(const e of t.filter((e=>function(e){return e&&"/"!==e}(e))))if(r){const t=e.replace(V,"");r=B(r)+t}else r=e;return r}(a,e)}(i.request,i.options.baseURL)),i.options.query&&(i.request=function(e,t){const r=function(e=""){const t=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(t){const[,e,r=""]=t;return{protocol:e.toLowerCase(),pathname:r,href:e+r,auth:"",host:"",search:"",hash:""}}if(!F(e,{acceptRelative:!0}))return z(e);const[,r="",a,s=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,n="",o=""]=s.match(/([^#/?]*)(.*)?/)||[];"file:"===r&&(o=o.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:i,search:d,hash:l}=z(o);return{protocol:r.toLowerCase(),auth:a?a.slice(0,Math.max(0,a.length-1)):"",host:n,pathname:i,search:d,hash:l,[j]:!r}}(e),a={...L(r.search),...t};return r.search=function(e){return Object.keys(e).filter((t=>void 0!==e[t])).map((t=>{return r=t,"number"!=typeof(a=e[t])&&"boolean"!=typeof a||(a=String(a)),a?Array.isArray(a)?a.map((e=>`${D(r)}=${M(e)}`)).join("&"):`${D(r)}=${M(a)}`:D(r);var r,a})).filter(Boolean).join("&")}(a),function(e){const t=e.pathname||"",r=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",a=e.hash||"",s=e.auth?e.auth+"@":"",n=e.host||"";return(e.protocol||e[j]?(e.protocol||"")+"//":"")+s+n+t+r+a}(r)}(i.request,i.options.query),delete i.options.query),"query"in i.options&&delete i.options.query,"params"in i.options&&delete i.options.params),i.options.body&&G(i.options.method)&&(function(e){if(void 0===e)return!1;const t=typeof e;return"string"===t||"number"===t||"boolean"===t||null===t||"object"===t&&(!!Array.isArray(e)||!e.buffer&&(e.constructor&&"Object"===e.constructor.name||"function"==typeof e.toJSON))}(i.options.body)?(i.options.body="string"==typeof i.options.body?i.options.body:JSON.stringify(i.options.body),i.options.headers=new a(i.options.headers||{}),i.options.headers.has("content-type")||i.options.headers.set("content-type","application/json"),i.options.headers.has("accept")||i.options.headers.set("accept","application/json")):("pipeTo"in i.options.body&&"function"==typeof i.options.body.pipeTo||"function"==typeof i.options.body.pipe)&&("duplex"in i.options||(i.options.duplex="half"))),!i.options.signal&&i.options.timeout){const e=new s;d=setTimeout((()=>{const t=new Error("[TimeoutError]: The operation was aborted due to timeout");t.name="TimeoutError",t.code=23,e.abort(t)}),i.options.timeout),i.options.signal=e.signal}try{i.response=await r(i.request,i.options)}catch(e){return i.error=e,i.options.onRequestError&&await Q(i,i.options.onRequestError),await n(i)}finally{d&&clearTimeout(d)}if((i.response.body||i.response._bodyInit)&&!X.has(i.response.status)&&"HEAD"!==i.options.method){const e=(i.options.parseResponse?"json":i.options.responseType)||function(e=""){if(!e)return"json";const t=e.split(";").shift()||"";return J.test(t)?"json":H.has(t)||t.startsWith("text/")?"text":"blob"}(i.response.headers.get("content-type")||"");switch(e){case"json":{const e=await i.response.text(),t=i.options.parseResponse||_;i.response._data=t(e);break}case"stream":i.response._data=i.response.body||i.response._bodyInit;break;default:i.response._data=await i.response[e]()}}return i.options.onResponse&&await Q(i,i.options.onResponse),!i.options.ignoreResponseError&&i.response.status>=400&&i.response.status<600?(i.options.onResponseError&&await Q(i,i.options.onResponseError),await n(i)):i.response},i=async function(e,t){return(await o(e,t))._data};return i.raw=o,i.native=(...e)=>r(...e),i.create=(r={},a={})=>e({...t,...a,defaults:{...t.defaults,...a.defaults,...r}}),i}({fetch:Y.fetch?(...e)=>Y.fetch(...e):()=>Promise.reject(new Error("[ofetch] global.fetch is not supported!")),Headers:Y.Headers,AbortController:Y.AbortController});function te(e,t,r){return r}function re(){return Math.floor(9e5*Math.random())+1e5}function ae(e,t){const r=new RegExp(`"${e}":"([^"]+)"`).exec(t);return r?.[1]}class se extends u{constructor(){super(),this.models={"gemini-1.5-flash":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"418ab5ea040b5c43"]'},"gemini-1.5-pro":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"9d60dfae93c9ff1f"]'},"gemini-1.5-pro-research":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"e5a44cb1dae2b489"]'},"gemini-2.0-flash":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"f299729663a2343f"]'},"gemini-2.0-flash-thinking":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"9c17b1863f581b8a"]'},"gemini-2.0-flash-thinking-with-apps":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"f8f8f5ea629f5d37"]'},"gemini-2.0-exp-advanced":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"b1e46a6037e6aa9f"]'},"gemini-2.5-flash-exp":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"35609594dbe934d8"]'},"gemini-2.5-pro-exp":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"2525e3954d185b3c"]'},"gemini-deepresearch":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"cd472a54d2abba7e"]'}},this.defaultModel="gemini-2.0-flash",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidGeminiMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidGeminiMetadata(e.metadata))))}isValidGeminiMetadata(e){return void 0!==e?.conversationId&&e?.contextIds&&Array.isArray(e.contextIds)&&3===e.contextIds.length&&e?.requestParams?.atValue&&e.requestParams?.blValue&&e.requestParams?.sid&&"string"==typeof e?.emoji&&"string"==typeof e?.defaultLang&&e.defaultLang&&"string"==typeof e?.defaultModel&&e.defaultModel&&"string"==typeof e?.shareUrl}getGeminiMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.INVALID_REQUEST);const r=t.metadata;return this.isValidGeminiMetadata(r)?r:this.handleModelError("Invalid thread metadata",e.INVALID_REQUEST)}getName(){return"Google Bard"}supportsImageInput(){return!0}async initNewThread(){this.currentThread={id:h(),title:"New Conversation",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:{conversationId:"",contextIds:["","",""],requestParams:await this.fetchRequestParams(),emoji:"",defaultLang:"en",defaultModel:"2.0 Flash",shareUrl:""}},await this.saveThread()}async fetchRequestParams(){try{const t=await ee("https://gemini.google.com/",{responseType:"text"}),r=ae("SNlM0e",t),a=ae("cfb2h",t),s=ae("FdrFJe",t);return r&&a&&s?{atValue:r,blValue:a,sid:s}:this.handleModelError("Failed to extract Bard parameters",e.UNAUTHORIZED)}catch(t){return this.handleModelError("Failed to initialize Bard session",e.UNAUTHORIZED,void 0,t)}}parseBardResponse(t){try{const r=t.split("\n").find((e=>e.includes('"rc_')));if(!r)return this.handleModelError("Could not find primary data line in response text for fallback parsing.",e.RESPONSE_PARSING_ERROR);let a;try{a=JSON.parse(r)}catch(t){return this.handleModelError(`Failed to parse data line JSON: ${t}`,e.RESPONSE_PARSING_ERROR,void 0,t)}if(!Array.isArray(a)||!a[0]||!a[0][2])return this.handleModelError("Unexpected structure in parsed data line.",e.RESPONSE_PARSING_ERROR);const s=a[0][2],n=JSON.parse(s);if(!n)return this.handleModelError("Empty response data",e.RESPONSE_PARSING_ERROR);const o=n[4]?.[0]?.[1]?.[0]??"",i=[n[1]?.[0]??"",n[1]?.[1]??"",n[4]?.[0]?.[0]??""],d=n[4]?.[0]?.[4]||[];let l=o;for(const e of d){const[t,r,a]=e;t&&r&&a&&t[0]?.[0]&&r[0]?.[0]&&t[4]&&(l=l.replace(a,`[![${t[4]}](${t[0][0]})](${r[0][0]})`))}return{text:l,ids:i}}catch(t){return this.handleModelError(`Failed to parse Bard response (fallback): ${t}`,e.RESPONSE_PARSING_ERROR,void 0,t)}}async uploadImage(t){try{const r={"content-type":"application/x-www-form-urlencoded;charset=UTF-8","push-id":"feeds/mcudyrk2a4khkz","x-goog-upload-header-content-length":t.size.toString(),"x-goog-upload-protocol":"resumable","x-tenant-id":"bard-storage"},a=(await ee.raw("https://content-push.googleapis.com/upload/",{method:"POST",headers:{...r,"x-goog-upload-command":"start"},body:new URLSearchParams({[`File name: ${t.name}`]:""})})).headers.get("x-goog-upload-url");return a?await ee(a,{method:"POST",headers:{...r,"x-goog-upload-command":"upload, finalize","x-goog-upload-offset":"0"},body:t}):this.handleModelError("Failed to get upload URL for image",e.SERVICE_UNAVAILABLE)}catch(r){return this.handleModelError(`Failed to finalize image upload: ${t.name}`,e.UPLOAD_FAILED,void 0,r)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidGeminiMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}async doSendMessage(t){let r;t.images&&t.images.length>1&&this.handleModelError("Gemini Web only supports one image per message.",e.UPLOAD_AMOUNT_EXCEEDED,t);try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded(),r=this.getCurrentThreadSafe();const a=this.createMessage("user",t.prompt);r.messages.push(a);const s=this.getBardMetadata();let n,o;if(console.log("Current context IDs before request:",s.contextIds),t.images&&t.images.length>0){t.images.length>1&&console.warn("GeminiWebModel only supports one image per message. Using the first image."),o=t.images[0];try{n=await this.uploadImage(o),a.metadata={...a.metadata||{},attachmentUrl:n}}catch(r){this.handleModelError(`Failed to upload image: ${o.name}`,e.UPLOAD_FAILED,t,r)}}const i=[null,JSON.stringify([[t.prompt,0,null,n&&o?[[[n,1],o.name]]:[]],null,s.contextIds])],d=this.models[t.model||this.defaultModel],l=await fetch("https://gemini.google.com/_/BardChatUi/data/assistant.lamda.BardFrontendService/StreamGenerate",{method:"POST",signal:t.signal,headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8",...d||{}},body:new URLSearchParams({at:s.requestParams.atValue,"f.req":JSON.stringify(i),bl:s.requestParams.blValue,_reqid:String(re()),rt:"c"})});if(!l.ok||!l.body){const r=await l.text();return this.handleModelError(`Gemini API error: ${l.status} - ${r.substring(0,200)}`,e.SERVICE_UNAVAILABLE,t)}const c=l.body.getReader(),g=new TextDecoder;let h="",u="",m=null,p=!1;for(;;){const{done:e,value:a}=await c.read();if(e)break;let s;for(h+=g.decode(a,{stream:!0});(s=h.indexOf("\n"))>=0;){const e=h.slice(0,s).trim();if(h=h.slice(s+1),e&&!/^\d+$/.test(e)&&")]}'"!==e)try{const a=JSON.parse(e);if(Array.isArray(a)&&a.length>0&&Array.isArray(a[0])&&"wrb.fr"===a[0][0]){const e=a[0][2];if("string"==typeof e){const a=JSON.parse(e);if(!p&&r.messages.length<=1&&a&&a[10]&&Array.isArray(a[10])&&"string"==typeof a[10][0]){let e=a[10][0];e.endsWith("\n")&&(e=e.slice(0,-1)),e&&(t.onEvent({type:"TITLE_UPDATE",data:{title:e,threadId:r.id}}),r.title=e,p=!0)}if(a&&a[4]?.[0]?.[1]?.[0]){const e=a[4][0][1][0];e!==u&&(u=e,t.onEvent({type:"UPDATE_ANSWER",data:{text:u}}))}if(a&&a[1]?.[0]&&a[1]?.[1]&&a[4]?.[0]?.[0]&&(m=[a[1][0],a[1][1],a[4][0][0]],r.metadata)){const e=r.metadata;JSON.stringify(e.contextIds)!==JSON.stringify(m)&&(e.contextIds=m,console.log("Updated context IDs mid-stream:",m)),m[0]&&e.conversationId!==m[0]&&(e.conversationId=m[0],console.log("Updated conversationId mid-stream:",m[0]))}}}}catch(t){console.warn("Error parsing Gemini stream line:",e,t)}}}if(!m){console.warn("Final IDs not found in stream, attempting fallback parse.");try{m=this.parseBardResponse(u).ids}catch(r){return console.error("Fallback parsing failed:",r),this.handleModelError("Failed to extract final IDs from response stream",e.RESPONSE_PARSING_ERROR,t,r)}}const A=this.createMessage("assistant",u);if(A.metadata={messageId:m[1]},r.messages.push(A),r.metadata){const e=r.metadata;e.contextIds=m,e.conversationId=m[0]||""}r.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:r.id}})}catch(r){this.handleModelError("Error during Gemini message sending or processing",r instanceof s?r.code:e.NETWORK_ERROR,t,r)}}async loadThread(e){const t=await this.getAllThreads(),r=t.find((t=>t.id===e));r&&r.modelName===this.getName()&&(this.currentThread=r,this.currentThread.metadata.requestParams=await this.fetchRequestParams(),await this.saveThread(),await this.saveThreadsToStorage(t))}getBardMetadata(){const t=this.getCurrentThreadSafe();return t.metadata?this.isValidGeminiMetadata(t.metadata)?t.metadata:this.handleModelError("Invalid or incomplete thread metadata",e.INVALID_REQUEST):this.handleModelError("No thread metadata available",e.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.INVALID_REQUEST)}async saveThread(){if(!this.currentThread)return;const e=await this.getAllThreads(),t=e.findIndex((e=>e.id===this.currentThread.id));-1!==t?e[t]=this.currentThread:e.push(this.currentThread),await this.saveThreadsToStorage(e)}async editTitle(t,r,a){try{let s,n=!1!==a?.loadThread,o=!1!==a?.tryUpdateThread;if(console.log(n,o),n)await this.ensureThreadLoaded(),s=this.getBardMetadata();else{if(!a?.metadata)return this.handleModelError("No thread loaded and no metadata provided for title edit",e.INVALID_REQUEST);if(s=a.metadata,!this.isValidGeminiMetadata(s))return this.handleModelError("Invalid metadata provided for title edit",e.INVALID_REQUEST)}const i=s.conversationId;if(!i){const t=s.contextIds[0];if(!t)return this.handleModelError("Missing conversation ID in metadata for editTitle",e.INVALID_REQUEST);console.warn("Using fallback conversation ID from contextIds[0] for editTitle"),s.conversationId=t,await this.saveThread()}const d=[i,t];r&&d.push(null,null,r,null,null,null,null,null,[1,r]);const l=JSON.stringify([null,[["title","icon","user_selected_icon"]],d]),c=JSON.stringify([["MUAZcd",l,null,"generic"]]),g=new URL("https://gemini.google.com/_/BardChatUi/data/batchexecute");g.searchParams.set("rpcids","MUAZcd"),g.searchParams.set("source-path",`/app/${i}`),g.searchParams.set("bl",s.requestParams.blValue);const h=s.requestParams.sid??String(-Math.floor(9e18*Math.random()));g.searchParams.set("f.sid",h),g.searchParams.set("hl","en"),g.searchParams.set("_reqid",String(re())),g.searchParams.set("rt","c");let u=new URLSearchParams({at:s.requestParams.atValue,"f.req":"["+c+"]"});const m=await ee.raw(g.toString(),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:u.toString()+"&",parseResponse:t=>{const r=t.substring(t.indexOf("\n")+1).split("\n").find((e=>e.trim().startsWith('[["wrb.fr"')));return r?JSON.parse(r):(console.error("Raw editTitle response:",t),this.handleModelError("Could not find data line in editTitle response",e.RESPONSE_PARSING_ERROR))}});if(console.log("Raw editTitle response:",m),console.log("Parsed e response:",m?._data[0][0]),console.log("Parersed e response:",m?._data[0][1]),!m||"object"!=typeof m||"wrb.fr"!==m?._data[0][0]||"MUAZcd"!==m?._data[0][1]||!m?._data[0][2])return this.handleModelError("Title update failed. Server response did not indicate success.",e.UNKNOWN_ERROR);console.log("Title updated successfully on server.");try{const e=JSON.parse(m?._data[0][2]),t=e[1][1],r=e[1][4];console.log("Server response after title update:",e),console.log("Server title change confirmation received:",t),console.log(this.currentThread),o&&this.currentThread&&this.currentThread.metadata&&(console.log(this.currentThread),this.currentThread.title=t,this.currentThread.metadata.emoji=r||"",await this.saveThread(),console.log("Thread updated locally after title change confirmation."))}catch(t){return console.warn("Could not parse success response details:",t),console.error("Unexpected response structure after title update. Could not parse response:",m),this.handleModelError("Title update succeeded but response format unexpected",e.RESPONSE_PARSING_ERROR,void 0,t)}}catch(t){this.handleModelError("Error updating conversation title",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{const s=await this.getAllThreads();for(const n of t)try{const t=s.find((e=>e.metadata?.conversationId===n));if(!t){console.warn(`[deleteServerThreads] Thread ${n} not found locally.`);continue}if(t.modelName!==this.getName()){console.warn(`[deleteServerThreads] Thread ${n} has incorrect model name: ${t.modelName}. Skipping.`);continue}if(!this.isValidGeminiMetadata(t.metadata)){console.warn(`[deleteServerThreads] Thread ${n} has invalid or missing metadata. Cannot delete from server.`),r&&(console.warn(`[deleteServerThreads] Deleting thread ${n} locally due to invalid metadata.`),await this.deleteThread(t.id,a));continue}const o=t.metadata;let i=o.conversationId,d=o.requestParams.atValue,l=o.requestParams.blValue;if(!i){const e=o.contextIds[0];if(!e){console.warn(`[deleteServerThreads] Missing conversation ID in metadata for thread ${n}. Cannot delete from server.`),r&&(console.warn(`[deleteServerThreads] Deleting thread ${n} locally due to missing conversation ID.`),await this.deleteThread(t.id,a));continue}console.warn(`[deleteServerThreads] Using fallback conversation ID from contextIds[0] for thread ${n}.`),o.conversationId=e,i=e,await this.saveThread()}if(!d||!l){console.warn(`[deleteServerThreads] Missing 'at' or 'bl' value in requestParams for thread ${n}. Fetching fresh params.`);try{o.requestParams=await this.fetchRequestParams(),d=o.requestParams.atValue,l=o.requestParams.blValue,await this.saveThread()}catch(e){console.error(`[deleteServerThreads] Failed to refresh requestParams for thread ${n}. Skipping server delete.`,e),r&&(console.warn(`[deleteServerThreads] Deleting thread ${n} locally due to failed param refresh.`),await this.deleteThread(t.id,a));continue}}const c="https://gemini.google.com/_/BardChatUi/data/batchexecute",g=JSON.stringify([["GzXR5e",JSON.stringify([i]),null,"generic"]]),h=new URLSearchParams({rpcids:"GzXR5e","source-path":"/app",bl:l,"f.sid":o.requestParams.sid??String(-Math.floor(9e18*Math.random())),hl:"en",_reqid:String(re()),rt:"c"}),u=new URLSearchParams({at:d,"f.req":`[${g}]`});console.log(`[deleteServerThreads] Sending Request 1 for ${n} (ConvID: ${i})`);const m=await ee.raw(`${c}?${h.toString()}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:u.toString()+"&",parseResponse:e=>e});if(200!==m.status)return console.error(`[deleteServerThreads] Request 1 failed for ${n}. Status: ${m.status}`,await m._data),this.handleModelError(`Request 1 failed with status ${m.status}`,e.SERVICE_UNAVAILABLE);console.log(`[deleteServerThreads] Request 1 successful for ${n}.`);const p=JSON.stringify([["qWymEb",JSON.stringify([i,[1,null,0,1]]),null,"generic"]]),A=new URLSearchParams({rpcids:"qWymEb","source-path":"/app",bl:l,"f.sid":o.requestParams.sid??String(-Math.floor(9e18*Math.random())),hl:"en",_reqid:String(re()),rt:"c"}),f=new URLSearchParams({at:d,"f.req":`[${p}]`});console.log(`[deleteServerThreads] Sending Request 2 for ${n} (ConvID: ${i})`);const E=await ee.raw(`${c}?${A.toString()}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:f.toString()+"&",parseResponse:e=>e});if(200!==E.status)return console.error(`[deleteServerThreads] Request 2 failed for ${n}. Status: ${E.status}`,await E._data),this.handleModelError(`Request 2 failed with status ${E.status}`,e.SERVICE_UNAVAILABLE);console.log(`[deleteServerThreads] Request 2 successful for ${n}.`),r&&(console.log(`[deleteServerThreads] Deleting thread ${n} locally.`),await this.deleteThread(t.id,a))}catch(e){console.error(`[deleteServerThreads] Failed to process thread ${n}:`,e)}}catch(t){return this.handleModelError("Error during server thread deletion process",e.SERVICE_UNAVAILABLE,void 0,t)}}async getConversationData(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getBardMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for getting conversation data",e.INVALID_REQUEST);if(r=t.metadata,!this.isValidGeminiMetadata(r))return this.handleModelError("Invalid metadata provided for getting conversation data",e.INVALID_REQUEST)}const s=r.conversationId||r.contextIds[0];if(!s)return this.handleModelError("Missing conversation ID in metadata",e.INVALID_REQUEST);const n=JSON.stringify([s,10,null,1,[1],null,null,1]),o=JSON.stringify([["hNvQHb",n,null,"generic"]]),i=new URL("https://gemini.google.com/_/BardChatUi/data/batchexecute");i.searchParams.set("rpcids","hNvQHb"),i.searchParams.set("source-path",`/app/${s.substring(2)}`),i.searchParams.set("bl",r.requestParams.blValue);const d=r.requestParams.sid??String(-Math.floor(9e18*Math.random()));i.searchParams.set("f.sid",d),i.searchParams.set("hl","en"),i.searchParams.set("_reqid",String(re())),i.searchParams.set("rt","c");let l=new URLSearchParams({"f.req":`[${o}]`,at:r.requestParams.atValue});const c=await ee(i.toString(),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:l+"&",parseResponse:t=>{const r=t.substring(t.indexOf("\n")+1).split("\n").find((e=>e.trim().startsWith('[["wrb.fr"')));return r?JSON.parse(r):(console.error("Raw getConversationData response:",t),this.handleModelError("Could not find data line in getConversationData response",e.RESPONSE_PARSING_ERROR))}});if(console.log(c),!c||!Array.isArray(c)||0===c.length||!Array.isArray(c[0])||c[0].length<3)return console.error("Unexpected response structure in getConversationData:",c),this.handleModelError("Unexpected response structure in getConversationData",e.RESPONSE_PARSING_ERROR);const g=c[0][2],h=JSON.parse(g),u=[];if(Array.isArray(h?.[0]))for(const e of h[0].slice().reverse()){if(!Array.isArray(e)||e.length<4){console.warn("Skipping invalid message pair structure:",e);continue}const t=e[2],r=e[4];let a=null;if(Array.isArray(t)&&Array.isArray(t[0])&&"string"==typeof t[0][0]){const e=t[0][0],s=Array.isArray(r)&&"number"==typeof r[0]?r[0]:void 0;a=this.createMessage("user",e),void 0!==s&&(a.timestamp=s),a.metadata={}}else console.warn("Could not extract user message text from:",t);const s=e[3],n=e[4];let o=null;if(Array.isArray(s)&&Array.isArray(s[0])&&Array.isArray(s[0][0])){const e=s[0][0];if(Array.isArray(e)&&e.length>1&&Array.isArray(e[1])&&"string"==typeof e[1][0]){const t=e[1][0],r=Array.isArray(n)&&"number"==typeof n[0]?n[0]:void 0;o=this.createMessage("assistant",t),void 0!==r&&(o.timestamp=r),o.metadata={}}else console.warn("Could not extract assistant message text from:",e)}else console.warn("Could not extract assistant message wrapper from:",s);a&&u.push(a),o&&u.push(o)}else console.warn("No message pairs found in conversation payload:",h);return u}catch(t){return console.error("Error getting conversation data:",t),this.handleModelError(`Failed to get conversation data: ${t instanceof Error?t.message:String(t)}`,t instanceof s?t.code:e.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(t){try{let r=!1!==t?.loadThread;t?.metadata&&(r=!1),t||(t={});let a,s=t?.title||"",n=t?.modelName||"",o=t?.language||"";if(r)await this.ensureThreadLoaded(),a=this.getGeminiMetadata(),s||(s=this.currentThread?.title||""),n||(n=this.currentThread?.metadata?.defaultModel||""),o||(o=this.currentThread?.metadata?.defaultLang||"");else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t?.metadata))return this.handleModelError("Invalid metadata provided for sharing",e.INVALID_REQUEST);a=t.metadata,n=a.defaultModel||"",o=a.defaultLang||""}s||(console.warn('Title is required when sharing a conversation, but not provided or is blank. Using "Untitled Conversation" as title.'),s="Untitled Conversation"),n||(console.warn('Model name is required when sharing a conversation, but not provided. Using default model name "2.0 Flash".'),n="2.0 Flash"),o||(console.warn('Language is required when sharing a conversation, but not provided. Using default language "en".'),o="en");const i=[[["fuVx7",JSON.stringify([null,a.contextIds[0],null,a.contextIds[2],[1,s+"\n",null,null,null,["","",""],null,[null,null,n]],[o],0]),null,"generic"]]],d=new URLSearchParams({"f.req":JSON.stringify(i),at:a.requestParams.atValue}),l=`https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({rpcids:"fuVx7","source-path":`/app/${a.contextIds[0].substring(2)}`,bl:a.requestParams.blValue,"f.sid":a.requestParams.sid,hl:t.language||"en",_reqid:String(re()),rt:"c"})}`,c=await ee(l,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:d+"&",parseResponse:e=>e,onResponse:({response:t})=>{if(!t.ok)return this.handleModelError(`Failed to share conversation: ${t.status}`,e.SERVICE_UNAVAILABLE,void 0,t.statusText)}}),g=c.split("\n").find((e=>e.includes('"wrb.fr"')));if(!g)return this.handleModelError("Failed to parse Gemini share response",e.RESPONSE_PARSING_ERROR);console.log(g);let h="";try{const e=JSON.parse(g);if(console.log(e),e[0]&&Array.isArray(e[0])&&e[0][2]){const t=JSON.parse(e[0][2]);Array.isArray(t)&&t[2]&&(h=t[2])}}catch(t){return this.handleModelError("Error extracting share ID from Gemini response",e.RESPONSE_PARSING_ERROR,void 0,t)}if(!h)return this.handleModelError("No share ID found in Gemini response",e.RESPONSE_PARSING_ERROR);const u=`https://g.co/gemini/share/${h}`;return r&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=u,await this.saveThread()),u}catch(t){return this.handleModelError("Error sharing conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}async unShareConversation(t){try{let r,a=!1,s=!1!==t?.loadThread,n=!1!==t?.updateThread;if(t?.metadata&&(s=!1),t||(t={}),s)await this.ensureThreadLoaded(),r=this.getGeminiMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.INVALID_REQUEST);if(n)return this.handleModelError("Cannot update thread when LoadThread option is false (updateThread option is not supported)",e.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t?.metadata))return this.handleModelError("Invalid metadata provided for sharing",e.INVALID_REQUEST);r=t.metadata}if(!r.shareUrl||!r?.shareUrl.includes("https://g.co/gemini/share/"))return this.handleModelError("No share URL found in metadata",e.INVALID_REQUEST);const o=[[["SgORbf",JSON.stringify([null,r.shareUrl.replace("https://g.co/gemini/share/","")]),null,"generic"]]],i=new URLSearchParams({"f.req":JSON.stringify(o),at:r.requestParams.atValue}),d=`https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({rpcids:"fuVx7","source-path":`/app/${r.shareUrl.replace("https://g.co/gemini/share/","")}`,bl:r.requestParams.blValue,"f.sid":r.requestParams.sid,hl:r.defaultLang||"en",_reqid:String(re()),rt:"c"})}`,l=await ee(d,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:i+"&",parseResponse:e=>e,onResponse:({response:t})=>{if(!t.ok)return this.handleModelError(`Failed to un-share conversation: ${t.status}`,e.SERVICE_UNAVAILABLE,void 0,t.statusText)}}),c=l.split("\n").find((e=>e.includes('"wrb.fr"')));if(!c)return this.handleModelError("Failed to parse Gemini un-share response",e.RESPONSE_PARSING_ERROR);console.log(c);try{const e=JSON.parse(c);if(console.log(e),e[0]&&Array.isArray(e[0])&&e[0][2])return a="[]"===e[0][2],a&&n&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl="",await this.saveThread()),a}catch(t){return this.handleModelError("Failed to parse Gemini un-share response",e.RESPONSE_PARSING_ERROR,void 0,t)}return a}catch(t){return this.handleModelError("Error sharing conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}}function ne(e,t,r){return r}p([te,A("design:type",Function),A("design:paramtypes",[File]),A("design:returntype",Promise)],se.prototype,"uploadImage",null),p([te,A("design:type",Function),A("design:paramtypes",[String,String,Object]),A("design:returntype",Promise)],se.prototype,"editTitle",null),p([te,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean]),A("design:returntype",Promise)],se.prototype,"deleteServerThreads",null),p([te,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],se.prototype,"getConversationData",null),p([te,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],se.prototype,"shareConversation",null),p([te,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],se.prototype,"unShareConversation",null),"undefined"!=typeof WebSocket?WebSocket:"undefined"!=typeof MozWebSocket?MozWebSocket:void 0!==r.g?r.g.WebSocket||r.g.MozWebSocket:"undefined"!=typeof window?window.WebSocket||window.MozWebSocket:"undefined"!=typeof self&&(self.WebSocket||self.MozWebSocket);class oe extends u{constructor(e={}){super(),this.sessionKey=e.sessionKey,this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidClaudeMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidClaudeMetadata(e.metadata))))}isValidClaudeMetadata(e){return e?.organizationId&&e?.conversationId}getName(){return"Claude Web"}supportsImageInput(){return!0}async createConversation(t){const r=h();try{return await ee(`https://claude.ai/api/organizations/${t}/chat_conversations`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:{name:"",uuid:r}}),r}catch(t){return t instanceof q&&403===t.status?this.handleModelError("There is no logged-in Claude account in this browser.",e.UNAUTHORIZED,void 0,t):this.handleModelError("Failed to create conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}async generateChatTitle(e,t,r){try{const a=await ee(`https://claude.ai/api/organizations/${e}/chat_conversations/${t}/title`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:{message_content:r,recent_titles:[]}});return a.title?a.title:"New Conversation"}catch(e){return console.error("Failed to generate chat title:",e),"New Conversation"}}getHeaders(){const e={"Content-Type":"application/json",Accept:"application/json"};return this.sessionKey&&(e.Cookie=`sessionKey=${this.sessionKey}`),e}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidClaudeMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getClaudeMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.INVALID_REQUEST);const r=t.metadata;return r.organizationId&&r.conversationId?r:this.handleModelError("Invalid thread metadata",e.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.INVALID_REQUEST)}async initNewThread(){try{const t=await this.getOrganizationId();if(!t)return this.handleModelError("Organization ID is required",e.INVALID_REQUEST);const r=await this.createConversation(t);this.currentThread={id:r,title:"New Conversation",modelName:this.getName(),messages:[],createdAt:Date.now(),updatedAt:Date.now(),metadata:{organizationId:t,conversationId:r}},await this.saveThread()}catch(t){return this.handleModelError("Error initializing new thread",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r||r.modelName!==this.getName())return this.handleModelError(`Thread ${t} not found`,e.INVALID_THREAD_ID);this.currentThread=r,!this.organizationId&&r.metadata&&(this.organizationId=r.metadata.organizationId)}async doSendMessage(t){t.images&&t.images.length>1&&this.handleModelError("Claude Web only supports one image per message.",e.UPLOAD_AMOUNT_EXCEEDED,t);try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded();const r=this.getCurrentThreadSafe(),a=this.getClaudeMetadata();let s;t.images&&t.images.length>0&&(t.images.length>1&&console.warn("ClaudeWebModel currently only supports one image per message. Using the first image."),s=t.images[0]);const n=this.createMessage("user",t.prompt);s&&(n.metadata={...n.metadata||{},imageDataUrl:await this.fileToDataUrl(s)}),r.messages.push(n);let o={},i=[];if(s){const r=new FormData;r.append("file",s);try{const s=await fetch(`https://claude.ai/api/${a.organizationId}/upload`,{method:"POST",credentials:"include",body:r});if(!s.ok){const r=await s.text();return this.handleModelError(`Failed to upload image: ${r}`,e.UPLOAD_FAILED,t,r)}if(o=await s.json(),!o||!o.file_uuid)return this.handleModelError("Invalid upload response format",e.UPLOAD_FAILED,t);i=[{file_name:o.file_name,file_size:o.file_size,file_type:o.file_type,file_uuid:o.file_uuid,source:"file_upload"}]}catch(r){this.handleModelError(`Failed to upload image: ${s.name}`,e.UPLOAD_FAILED,t,r)}}let d=await this.getStyles(a.organizationId),l=this.findStyleByKey(d,t.style_key||"");t.style_key&&!l&&console.warn(`Style key '${t.style_key}' not found, using default style.`);const c=await fetch(`https://claude.ai/api/organizations/${a.organizationId}/chat_conversations/${a.conversationId}/completion`,{method:"POST",headers:this.getHeaders(),credentials:"include",signal:t.signal,body:JSON.stringify({attachments:i,files:[],locale:navigator.language||"en-US",personalized_styles:l?[l]:[],prompt:t.prompt,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone})});if(!c.ok){const r=await c.text();try{const a=JSON.parse(r);if(429===c.status&&"error"===a.type&&"rate_limit_error"===a.error?.type)try{a.error.message=JSON.parse(a.error.message);let r="";if(a.error.resetsAt){const e=new Date(1e3*a.error.resetsAt);a.error.resetsAt.resetsAtReadable=e.toLocaleString(),r=` Rate limit resets at ${a.error.resetsAt.resetsAtReadable}`}return this.handleModelError(`Claude rate limit exceeded.${r}`,e.RATE_LIMIT_EXCEEDED,t,a)}catch(r){return this.handleModelError(`Claude rate limit exceeded: ${a.error.message}`,e.RATE_LIMIT_EXCEEDED,t,a)}return this.handleModelError(`Claude API error: ${JSON.stringify(a)}`,e.SERVICE_UNAVAILABLE,t)}catch(a){return 429===c.status?this.handleModelError("Claude rate limit exceeded. Please try again later.",e.RATE_LIMIT_EXCEEDED,t):this.handleModelError(`Claude API error: ${c.status} - ${r.substring(0,200)}`,e.SERVICE_UNAVAILABLE,t)}}if(!c.body)return this.handleModelError("Response body is null",e.SERVICE_UNAVAILABLE,t);const g=c.body.getReader(),h=new TextDecoder;let u="",m="",p="",A="";for(;;){const{done:e,value:r}=await g.read();if(e)break;u+=h.decode(r,{stream:!0});const a=u.split("\n");u=a.pop()||"";for(const e of a)if(e.trim())e.startsWith("event:")?p=e.substring(6).trim():e.startsWith("data:")&&(A=e.substring(5).trim());else if(p&&A){const e=this.processEvent(p,A,m);null!==e&&(m=e,t.onEvent({type:"UPDATE_ANSWER",data:{text:m}})),p="",A=""}}if(u.trim()){const e=u.split("\n");for(const r of e)if(r.startsWith("event:"))p=r.substring(6).trim();else if(r.startsWith("data:")&&(A=r.substring(5).trim(),p&&A)){const e=this.processEvent(p,A,m);null!==e&&(m=e,t.onEvent({type:"UPDATE_ANSWER",data:{text:m}}))}}const f=this.createMessage("assistant",m);if(r.messages.push(f),r.updatedAt=Date.now(),"New Conversation"===r.title&&r.messages.length<=2){const e=await this.generateChatTitle(a.organizationId,a.conversationId,t.prompt);r.title=e,t.onEvent({type:"TITLE_UPDATE",data:{title:e,threadId:r.id}})}await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:r.id}})}catch(r){this.handleModelError("Error sending message",e.SERVICE_UNAVAILABLE,t,r)}}processEvent(t,r,a,s){if(!t||!r)return null;try{switch(t){case"completion":const n=JSON.parse(r);if(n.completion)return a+n.completion;break;case"error":const o=JSON.parse(r);if("rate_limit_error"!==o.type)return this.handleModelError(o.error||o.message||"Unknown Claude error",o.error||o.message?e.SERVICE_UNAVAILABLE:e.UNKNOWN_ERROR,s||void 0,o);try{o.message=JSON.parse(o.message);let t="";if(o.resetsAt){const e=new Date(1e3*o.resetsAt);o.resetsAt.resetsAtReadable=e.toLocaleString(),t=` Rate limit resets at ${o.resetsAt.resetsAtReadable}`}return this.handleModelError(`Claude rate limit exceeded.${t}`,e.RATE_LIMIT_EXCEEDED,s||void 0,o)}catch(t){return this.handleModelError(`Claude rate limit exceeded: ${o.message}`,e.RATE_LIMIT_EXCEEDED,s||void 0,o)}case"ping":return null;default:return console.log(`Unhandled event type: ${t}`,r),null}}catch(r){console.warn(`Error processing ${t} event:`,r),this.handleModelError(`Error processing ${t} event`,e.UNKNOWN_ERROR,s,r)}return null}async fileToDataUrl(e){return new Promise(((t,r)=>{const a=new FileReader;a.onload=()=>t(a.result),a.onerror=r,a.readAsDataURL(e)}))}async editTitle(t,r){try{let a,s=!1!==r?.loadThread,n=!1!==r?.tryUpdateThread;if(r?.metadata&&(s=!1),s)await this.ensureThreadLoaded(),a=this.getClaudeMetadata();else{if(!r?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.INVALID_REQUEST);if(a=r.metadata,!a.organizationId||!a.conversationId)return this.handleModelError("Invalid metadata provided for sharing",e.INVALID_REQUEST)}const o=await fetch(`https://claude.ai/api/organizations/${a.organizationId}/chat_conversations/${a.conversationId}`,{method:"PUT",headers:this.getHeaders(),credentials:"include",body:JSON.stringify({name:t})});if(!o.ok)return this.handleModelError(`Failed to update title: ${o.status}`,e.SERVICE_UNAVAILABLE,void 0,await o.text());n&&this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError("Error updating conversation title",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0,s){try{if(s||(s=await this.getOrganizationId()),!s||!t)return this.handleModelError("Invalid metadata provided for request",e.INVALID_REQUEST);const n=await fetch(`https://claude.ai/api/organizations/${s}/chat_conversations/delete_many`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:JSON.stringify({conversation_uuids:t})});if(!n.ok)return this.handleModelError(`Failed to get conversation: ${n.status}`,e.SERVICE_UNAVAILABLE,void 0,await n.text());let o=await n.json();if(r)for(let e of t)await this.deleteThread(e,a);return o}catch(t){return this.handleModelError("Error deleting conversation(s)",e.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getClaudeMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.INVALID_REQUEST);if(r=t.metadata,!r.organizationId||!r.conversationId)return this.handleModelError("Invalid metadata provided for sharing",e.INVALID_REQUEST)}const s=await fetch(`https://claude.ai/api/organizations/${r.organizationId}/chat_conversations/${r.conversationId}/share`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:JSON.stringify({})});if(!s.ok){const t=await s.text();return this.handleModelError(`Failed to share conversation: ${s.status}`,e.SERVICE_UNAVAILABLE,void 0,t)}const n=await s.json();if(!n.uuid)return this.handleModelError("Share response did not contain a URL",e.SERVICE_UNAVAILABLE);const o=`https://claude.ai/share/${n.uuid}`;return a&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=o,await this.saveThread()),o}catch(t){return this.handleModelError("Error sharing conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}findStyleByKey(e,t){if(e&&Array.isArray(e.defaultStyles))for(let r=0;r<e.defaultStyles.length;r++)if(e.defaultStyles[r]&&e.defaultStyles[r].key===t)return e.defaultStyles[r];if(e&&Array.isArray(e.customStyles))for(let r=0;r<e.customStyles.length;r++)if(e.customStyles[r]&&e.customStyles[r].key===t)return e.customStyles[r]}async getStyles(t){t||(t=await this.getOrganizationId());const r=await fetch(`https://claude.ai/api/organizations/${t}/list_styles`,{method:"GET",headers:this.getHeaders(),credentials:"include"});return r.ok?await r.json():this.handleModelError(`Failed to get styles: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}async getConversationData(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getClaudeMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for getting conversation",e.INVALID_REQUEST);if(r=t.metadata,!r.organizationId||!r.conversationId)return this.handleModelError("Invalid metadata provided for getting conversation",e.INVALID_REQUEST)}const s=await fetch(`https://claude.ai/api/organizations/${r.organizationId}/chat_conversations/${r.conversationId}`,{method:"GET",headers:this.getHeaders(),credentials:"include"});return s.ok?await s.json():this.handleModelError(`Failed to get conversation: ${s.status}`,e.SERVICE_UNAVAILABLE,void 0,await s.text())}catch(t){return this.handleModelError("Error getting conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}async getAllConversationsData(t){t||(t=await this.getOrganizationId());try{const r=await fetch(`https://claude.ai/api/organizations/${t}/chat_conversations`,{method:"GET",headers:this.getHeaders(),credentials:"include",redirect:"error",cache:"no-cache"});return r.ok?await r.json():this.handleModelError(`Failed to get conversations data: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}catch(t){return this.handleModelError("Error getting conversations",e.SERVICE_UNAVAILABLE,void 0,t)}}async getOrganizationData(t){t||(t=await this.getOrganizationId());try{const r=await fetch(`https://claude.ai/api/organizations/${t}`,{method:"GET",headers:this.getHeaders(),credentials:"include"});return r.ok?await r.json():this.handleModelError(`Failed to get organization data: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}catch(t){return this.handleModelError("Error getting organization",e.SERVICE_UNAVAILABLE,void 0,t)}}async getAllOrganizationsData(){try{const t=await fetch("https://claude.ai/api/organizations",{method:"GET",headers:this.getHeaders(),credentials:"include",redirect:"error",cache:"no-cache"});return 403===t.status?this.handleModelError("There is no logged-in Claude account in this browser.",e.UNAUTHORIZED):t.ok?await t.json():this.handleModelError(`Failed to get organization data: ${t.status}`,e.SERVICE_UNAVAILABLE,void 0,await t.text())}catch(t){return this.handleModelError("Error getting organization",e.SERVICE_UNAVAILABLE,void 0,t)}}async getOrganizationId(){if(this.organizationId)return this.organizationId;try{const t=await this.getAllOrganizationsData();return t&&t.length?(this.organizationId=t[0].uuid,this.organizationId):this.handleModelError("No organizations found for Claude account",e.UNAUTHORIZED)}catch(t){this.handleModelError("Claude webapp not available in your country or region",e.SERVICE_UNAVAILABLE,void 0,t)}}}function ie(e,t,r){return r}p([ne,A("design:type",Function),A("design:paramtypes",[String,Object]),A("design:returntype",Promise)],oe.prototype,"editTitle",null),p([ne,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean,Object]),A("design:returntype",Promise)],oe.prototype,"deleteServerThreads",null),p([ne,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],oe.prototype,"shareConversation",null),p([ne,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],oe.prototype,"getStyles",null),p([ne,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],oe.prototype,"getConversationData",null),p([ne,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],oe.prototype,"getAllConversationsData",null),p([ne,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],oe.prototype,"getOrganizationData",null),p([ne,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],oe.prototype,"getAllOrganizationsData",null),p([ne,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],oe.prototype,"getOrganizationId",null);class de extends u{constructor(e={}){super(),this.sessionKey=e.sessionKey,this.baseUrl="https://www.perplexity.ai",this.visitorId=h(),this.models={"Perplexity Sonar":["turbo","concise","non-reasoning","non-pro"],"Perplexity Pro Auto":["pplx_pro","copilot","non-reasoning","pro-limited"],"Perplexity Sonar Pro":["experimental","copilot","non-reasoning","pro-account"],"GPT-4.1":["gpt4o","copilot","non-reasoning","pro-account"],"Claude 3.7 Sonnet":["claude2","copilot","non-reasoning","pro-account"],"Gemini 2.5 Pro":["gemini2flash","copilot","non-reasoning","pro-account"],"Grok 3 Beta":["grok","copilot","non-reasoning","pro-account"],"Perplexity R1 1776":["r1","copilot","reasoning","pro-account"],"GPT-o4-mini":["o3mini","copilot","reasoning","pro-account"],"Claude 3.7 Sonnet Thinking":["claude37sonnetthinking","copilot","reasoning","pro-account"],"Perplexity Deep Research":["pplx_alpha","copilot","reasoning","pro-limited"]},this.defaultModel="Perplexity Sonar",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidPerplexityMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidPerplexityMetadata(e.metadata))))}isValidPerplexityMetadata(e){return e?.conversationId}getName(){return"Perplexity Web"}getModels(){return this.models}getSearchSources(){return["web","scholar","social"]}supportsImageInput(){return!0}async uploadImage(t){try{const r=`${this.baseUrl}/rest/uploads/create_upload_url`,a={filename:t.name,content_type:t.type,source:"default",file_size:t.size,force_image:!1};if(!this.csrfToken&&(await this.checkAuth(),!this.csrfToken))return this.handleModelError("Failed to obtain CSRF token for upload",e.UNAUTHORIZED);const s=await ee(r,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify(a)});if(!s||!s.s3_bucket_url||!s.fields)return this.handleModelError("Failed to get upload parameters from Perplexity",e.UPLOAD_FAILED);const n=s.s3_bucket_url,o=new FormData;for(const e in s.fields)o.append(e,s.fields[e]);o.append("file",t);const i=await ee(n,{method:"POST",body:o});return i&&i.secure_url?(console.log("Image uploaded successfully:",i.secure_url),i.secure_url):this.handleModelError("Failed to upload image to Cloudinary or parse response",e.UPLOAD_FAILED)}catch(t){return console.error("Perplexity image upload error:",t),this.handleModelError(`Image upload failed: ${t instanceof Error?t.message:String(t)}`,e.UPLOAD_FAILED,void 0,t)}}getHeaders(e=!1){const t={"Content-Type":"application/json",Accept:"application/json","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"};return this.sessionKey&&(t.Cookie=`__Secure-next-auth.session-token=${this.sessionKey}`),e&&this.csrfToken&&(t["x-csrf-token"]=this.csrfToken),t}async checkAuth(){try{if(!await async function(e){const t={origins:[e]};try{return!!await o.permissions.contains(t)||(console.log(`Requesting host permission for: ${e}`),await o.permissions.request(t))}catch(e){return console.error("Error requesting permissions:",e),!1}}(`${this.baseUrl}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.MISSING_HOST_PERMISSION);const t=await ee(`${this.baseUrl}/api/auth/csrf`,{method:"GET",headers:this.getHeaders()});t&&t.csrfToken&&(this.csrfToken=t.csrfToken);const r=await ee(`${this.baseUrl}/api/auth/session`,{method:"GET",headers:this.getHeaders()});if(r&&r.user){this.userInfo={id:r.user.id,username:r.user.name,image:r.user.image,subscriptionStatus:r.user.subscription_status||"unknown"};try{const e=await ee(`${this.baseUrl}/rest/user/settings`,{method:"GET",headers:this.getHeaders(!0)});e&&(this.userSettings=e)}catch(e){console.warn("Failed to get user settings:",e)}return this.userInfo}return null}catch(t){return t instanceof q&&401===t.status?null:this.handleModelError("Failed to check authentication with Perplexity",e.SERVICE_UNAVAILABLE,void 0,t)}}async checkRateLimit(){try{return(await ee(`${this.baseUrl}/rest/rate-limit`,{method:"GET",headers:this.getHeaders(!0)})).remaining}catch(t){return this.handleModelError("Failed to check rate limit",e.SERVICE_UNAVAILABLE,void 0,t)}}async getRecentThreads(){try{return(await ee(`${this.baseUrl}/rest/thread/list_recent`,{method:"GET",headers:this.getHeaders(!0)})).entries||[]}catch(t){return this.handleModelError("Failed to get recent threads",e.SERVICE_UNAVAILABLE,void 0,t)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidPerplexityMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getPerplexityMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.INVALID_REQUEST);const r=t.metadata;return r.conversationId?r:this.handleModelError("Invalid thread metadata",e.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.INVALID_REQUEST)}async initNewThread(){try{await this.checkAuth();const e=h(),t=h();this.currentThread={id:e,title:"New Conversation",modelName:this.getName(),messages:[],createdAt:Date.now(),updatedAt:Date.now(),metadata:{conversationId:e,frontendContextUuid:t}},await this.saveThread()}catch(t){return this.handleModelError("Error initializing new thread",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r||r.modelName!==this.getName())return this.handleModelError(`Thread ${t} not found`,e.INVALID_THREAD_ID);this.currentThread=r}async doSendMessage(t){let r=[],a=this.defaultModel;t.model?this.models[t.model]?a=t.model:console.warn(`Invalid model "${t.model}" provided. Using default model "${a}" instead.`):console.log(`No model provided, using default model "${a}".`);let s="internet";t.searchFocus?"internet"===t.searchFocus||"writing"===t.searchFocus?s=t.searchFocus:console.warn(`Invalid search focus "${t.searchFocus}" provided. Using default mode "internet" instead.`):console.log('No search focus provided, using default "internet" mode'),"internet"===t.searchFocus?t.searchSources?t.searchSources.every((e=>this.getSearchSources().includes(e)))?r=t.searchSources:console.warn(`Invalid search source(s) "${t.searchSources}" provided. Using default source "web" instead.`):(console.log('No search source provided, using default source "web".'),r=["web"]):(t.searchSources||""!=t.searchSources)&&console.warn(`Invalid search source(s) "${t.searchSources}" provided for a no internet search response. Ignoring search sources.`);try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded();const n=this.getCurrentThreadSafe();let o=[];if(t.images&&t.images.length>0){if(t.images.length>4)return this.handleModelError("Maximum of 4 images allowed per message.",e.UPLOAD_AMOUNT_EXCEEDED,t);for(const r of t.images)try{const e=await this.uploadImage(r);o.push(e)}catch(a){return this.handleModelError(`Failed to upload image: ${r.name}`,e.UPLOAD_FAILED,t,a)}}const i=this.createMessage("user",t.prompt);let d;o.length>0&&(i.metadata={...i.metadata||{},attachmentUrls:o}),n.messages.push(i);try{d=this.getPerplexityMetadata()}catch(r){if(!this.currentThread?.metadata)return this.handleModelError("Failed to initialize thread metadata",e.METADATA_INITIALIZATION_ERROR,t,r);d=this.currentThread.metadata}const l=await this.checkAuth();if(await this.checkRateLimit()<=0)return this.handleModelError("You have reached your rate limit for Perplexity queries",e.RATE_LIMIT_EXCEEDED);const c=h(),g=n.messages.length>1&&d.backendUuid&&d.readWriteToken,u={attachments:o,browser_history_summary:[],client_coordinates:null,frontend_uuid:c,is_incognito:!1,is_nav_suggestions_disabled:!1,is_related_query:!1,is_sponsored:!1,language:navigator.language||"en-US",mode:this.models[a][1],model_preference:this.models[a][0],prompt_source:"user",search_focus:s,search_recency_filter:null,send_back_text_in_streaming_api:!1,sources:r,supported_block_use_cases:["answer_modes","media_items","knowledge_cards","inline_entity_cards","place_widgets","finance_widgets","sports_widgets","shopping_widgets","jobs_widgets","search_result_widgets","entity_list_answer","todo_list"],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,use_schematized_api:!0,user_nextauth_id:l?.id,version:"2.18",visitor_id:this.visitorId};let m;g?(m={last_backend_uuid:d.backendUuid,read_write_token:d.readWriteToken,query_source:"followup"},console.log("Sending follow-up request with backendUuid:",d.backendUuid)):(m={frontend_context_uuid:d.frontendContextUuid||h(),query_source:"home"},console.log("Sending first request with frontendContextUuid:",m.frontend_context_uuid));const p={params:{...u,...m},query_str:t.prompt},A=await fetch(`${this.baseUrl}/rest/sse/perplexity_ask`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify(p),signal:t.signal});if(!A.ok){const r=await A.text();return 429===A.status?this.handleModelError("Perplexity rate limit exceeded. Please try again later.",e.RATE_LIMIT_EXCEEDED,t):this.handleModelError(`Perplexity API error: ${A.status} - ${r.substring(0,200)}`,e.SERVICE_UNAVAILABLE,t)}if(!A.body)return this.handleModelError("Response body is null",e.SERVICE_UNAVAILABLE,t);const f=A.body.getReader(),E=new TextDecoder;let y="",w="",_=d.threadUrlSlug||"",T=d.backendUuid||"",v=d.contextUuid||"",S=d.readWriteToken||"";for(;;){const{done:e,value:r}=await f.read();if(e)break;const a=E.decode(r,{stream:!0});let s;for(y+=a,console.log("Perplexity chunk received:",a.length);(s=y.indexOf("\n"))>=0;){const e=y.slice(0,s).trim();if(y=y.slice(s+1),e&&e.startsWith("event:")){const r=e.substring(6).trim();let a=y.indexOf("\n");if(!(a>=0)){y=e+"\n"+y;break}{const e=y.slice(0,a).trim();if(y=y.slice(a+1),e.startsWith("data:")){const a=e.substring(5).trim();if("end_of_stream"===r){console.log("Perplexity end_of_stream received");continue}if("message"===r)try{const e=JSON.parse(a);let r=!1;if(e.thread_url_slug&&!_&&(_=e.thread_url_slug,d.threadUrlSlug=_),e.backend_uuid&&!T&&(T=e.backend_uuid,d.backendUuid=T),e.context_uuid&&(v=e.context_uuid,d.contextUuid=v),e.blocks&&Array.isArray(e.blocks))for(const t of e.blocks)if("ask_text"===t.intended_usage&&t.markdown_block){const e=t.markdown_block;Array.isArray(e.chunks)&&1==e.chunks.length&&(w+=e.chunks[0],r=!0)}r&&(t.onEvent({type:"UPDATE_ANSWER",data:{text:w}}),console.log("Perplexity text update:",w.length)),!0!==e.final&&!0!==e.final_sse_message||(e.thread_title&&e.thread_title!==n.title&&(n.title=e.thread_title,t.onEvent({type:"TITLE_UPDATE",data:{title:e.thread_title,threadId:n.id}})),e.read_write_token&&(S=e.read_write_token,d.readWriteToken=S),e.related_queries&&Array.isArray(e.related_queries)&&t.onEvent({type:"SUGGESTED_RESPONSES",data:{suggestions:e.related_queries.map((e=>"string"==typeof e?e:e.text)).filter(Boolean)}}))}catch(e){console.warn("Error parsing Perplexity message data JSON:",a,e)}}}}}}console.log("Perplexity stream finished, final text length:",w.length);const b=this.createMessage("assistant",w);n.messages.push(b),n.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:n.id}})}catch(r){return this.handleModelError("Error sending message",e.SERVICE_UNAVAILABLE,t,r)}}async getModelVersion(){try{const t=await ee(`${this.baseUrl}/rest/version`,{method:"GET",headers:this.getHeaders(!0)});return t&&t.version?t.version:this.handleModelError("Invalid response from Perplexity version endpoint",e.SERVICE_UNAVAILABLE)}catch(t){return this.handleModelError("Error fetching Perplexity version",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{if(!await this.checkAuth())return this.handleModelError("You must be logged in to delete conversations",e.UNAUTHORIZED);const s=await this.getAllThreads();for(const n of t)try{const t=s.find((e=>e.id===n));if(!t){console.warn(`Thread ${n} not found in storage.`);continue}if(t.modelName!==this.getName()){console.warn(`Thread ${n} has incorrect model name: ${t.modelName}`);continue}const o=t.metadata;if(!o||!o.backendUuid||!o.readWriteToken){r?(console.warn(`Thread ${n} has incomplete metadata. Cannot delete from server, deleting only from local storage.`),await this.deleteThread(n)):console.warn(`Thread ${n} has incomplete metadata. Cannot delete from server, skipping thread.`);continue}let i=await ee(`${this.baseUrl}/rest/thread/delete_thread_by_entry_uuid`,{method:"DELETE",headers:this.getHeaders(!0),body:JSON.stringify({entry_uuid:o.backendUuid,read_write_token:o.readWriteToken})});if(!i||"success"!==i.status)return this.handleModelError(`Failed to delete thread ${n} from Perplexity`,e.SERVICE_UNAVAILABLE,void 0,i?.detail||"Unknown error");r&&await this.deleteThread(n,a)}catch(e){console.error(`Error deleting thread ${n}:`,e)}}catch(t){return this.handleModelError("Error deleting conversations",e.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(){try{await this.ensureThreadLoaded();const t=this.getPerplexityMetadata();if(!await this.checkAuth())return this.handleModelError("You must be logged in to share conversations",e.UNAUTHORIZED);if(!t.contextUuid||!t.threadUrlSlug)return this.handleModelError("This conversation cannot be shared",e.FEATURE_NOT_SUPPORTED);const r=await ee(`${this.baseUrl}/rest/thread/update_thread_access`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:t.contextUuid,updated_access:2,read_write_token:t.readWriteToken})});return"success"!==r.status||2!==r.access?this.handleModelError("Failed to make conversation shareable",e.SERVICE_UNAVAILABLE):`${this.baseUrl}/search/${t.threadUrlSlug}`}catch(t){return this.handleModelError("Error sharing conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}async unShareConversation(){try{await this.ensureThreadLoaded();const t=this.getPerplexityMetadata();if(!await this.checkAuth())return this.handleModelError("You must be logged in to change conversation visibility",e.UNAUTHORIZED);if(!t.contextUuid)return this.handleModelError("This conversation cannot be modified",e.FEATURE_NOT_SUPPORTED);const r=await ee(`${this.baseUrl}/rest/thread/update_thread_access`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:t.contextUuid,updated_access:1,read_write_token:t.readWriteToken})});return"success"===r.status&&1===r.access}catch(t){return this.handleModelError("Error setting conversation to private",e.SERVICE_UNAVAILABLE,void 0,t)}}async editTitle(t){try{if(await this.ensureThreadLoaded(),!await this.checkAuth())return this.handleModelError("You must be logged in to edit conversation titles",e.UNAUTHORIZED);const r=this.getPerplexityMetadata();if(!r.contextUuid)return void(this.currentThread&&(this.currentThread.title=t,await this.saveThread()));await ee(`${this.baseUrl}/rest/thread/set_thread_title`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:r.contextUuid,title:t,read_write_token:r.contextUuid})}),this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError("Error updating conversation title",e.SERVICE_UNAVAILABLE,void 0,t)}}}p([ie,A("design:type",Function),A("design:paramtypes",[File]),A("design:returntype",Promise)],de.prototype,"uploadImage",null),p([ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],de.prototype,"checkAuth",null),p([ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],de.prototype,"checkRateLimit",null),p([ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],de.prototype,"getRecentThreads",null),p([ie,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean]),A("design:returntype",Promise)],de.prototype,"deleteServerThreads",null),p([ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],de.prototype,"shareConversation",null),p([ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],de.prototype,"unShareConversation",null),p([ie,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],de.prototype,"editTitle",null);let le=null,ce=null,ge=null,he=new Uint8Array(0),ue=new DataView(new ArrayBuffer(0));function me(){if(!ce)throw new Error("WASM Memory not initialized for view update");he.buffer!==ce.buffer&&(he=new Uint8Array(ce.buffer),ue=new DataView(ce.buffer))}const pe=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}),Ae=new TextEncoder;function fe(e,t){return 0===t?"":(me(),pe.decode(he.subarray(e,e+t)))}let Ee=[void 0,null,!0,!1],ye=Ee.length;function we(e){ye===Ee.length&&Ee.push(Ee.length+1);const t=ye;return ye=Ee[t],Ee[t]=e,t}function _e(e){return Ee[e]}function Te(e){e<4||void 0!==Ee[e]&&(Ee[e]=ye,ye=e)}let ve=0;function Se(e,t){const r=Ae.encode(e),a=t(r.length,1)>>>0;return me(),he.subarray(a,a+r.length).set(r),ve=r.length,a}const be={__wbindgen_string_new:(e,t)=>we(fe(e,t)),__wbindgen_object_clone_ref:e=>we(_e(e)),__wbindgen_object_drop_ref:Te,__wbindgen_throw:(e,t)=>{throw new Error(fe(e,t))},__wbindgen_number_new:e=>we(e),__wbindgen_number_get:(e,t)=>{if(!ce)throw new Error("WASM memory not available");me();const r=_e(e);"number"==typeof r?(ue.setFloat64(t,r,!0),ue.setInt32(t+8,0,!0)):(ue.setFloat64(t,NaN,!0),ue.setInt32(t+8,1,!0))},__wbindgen_is_undefined:e=>void 0===_e(e),__wbindgen_boolean_get:e=>{const t=_e(e);return"boolean"==typeof t?t?1:0:2},__wbindgen_string_get:(e,t)=>{if(!ce||!le?.__wbindgen_export_0)return console.error("WASM memory or malloc (__wbindgen_export_0) not available for __wbindgen_string_get"),void(ce&&(me(),ue.setInt32(t,0,!0),ue.setInt32(t+4,0,!0)));me();const r=_e(e);if("string"==typeof r){const e=Se(r,le.__wbindgen_export_0),a=ve;ue.setInt32(t,e,!0),ue.setInt32(t+4,a,!0)}else ue.setInt32(t,0,!0),ue.setInt32(t+4,0,!0)},__wbindgen_error_new:(e,t)=>we(new Error(fe(e,t))),__wbindgen_jsval_loose_eq:(e,t)=>_e(e)==_e(t),crypto_getRandomValues:(e,t)=>{me(),crypto.getRandomValues(he.subarray(e,e+t))},performance_now:()=>performance.now(),__wbindgen_thread_destroy:()=>{console.warn("STUB: __wbindgen_thread_destroy called")},__wbindgen_current_thread_destroy:()=>{console.warn("STUB: __wbindgen_current_thread_destroy called")},__wbindgen_thread_spawn:e=>(console.warn("STUB: __wbindgen_thread_spawn called with ptr:",e),0),__wbindgen_cb_drop:e=>"function"==typeof _e(e)&&(Te(e),!0)};async function xe(e){if(console.log("Starting PoW solve using WASM (Ported JS Logic):",e),"DeepSeekHashV1"!==e.algorithm)throw new Error("Unsupported PoW algorithm: "+e.algorithm);if("number"!=typeof e.difficulty)throw new Error("Missing difficulty in challenge object");if("number"!=typeof e.expire_at)throw new Error("Missing expire_at in challenge object");const t=await async function(){return ge||(ge=(async()=>{if(le)return le;const e=o.runtime.getURL("assets/sha3_wasm_bg.7b9ca65ddd.wasm");console.log("Initializing DeepSeek WASM from:",e);try{const t=fetch(e);ce=new WebAssembly.Memory({initial:17,maximum:16384,shared:!1}),me();const r={env:{...be,memory:ce}};console.log("Attempting WASM instantiation with imports:",Object.keys(r.env));const{instance:a}=await WebAssembly.instantiateStreaming(t,r);return a.exports.memory instanceof WebAssembly.Memory&&a.exports.memory!==ce?(console.log("WASM module exported its own memory instance. Updating reference."),ce=a.exports.memory,me()):console.log("WASM using provided memory instance."),le=a.exports,console.log("DeepSeek WASM module loaded and initialized successfully."),Ee=[void 0,null,!0,!1],ye=Ee.length,le}catch(e){throw console.error("Failed to load or instantiate WASM module:",e),ce=null,le=null,ge=null,Ee=[void 0,null,!0,!1],ye=Ee.length,e}})(),ge)}();if(!t||!ce)throw new Error("WASM module is not initialized.");const{__wbindgen_export_0:r,__wbindgen_export_1:a,__wbindgen_add_to_stack_pointer:s,wasm_solve:n}=t;if(!(r&&a&&s&&n))throw console.error("Available WASM exports:",Object.keys(t)),new Error("Required WASM exports not found after loading.");me();const{challenge:i,salt:d,difficulty:l,signature:c,target_path:g,expire_at:h}=e,u=i,m=`${d}_${h}_`;let p=0,A=0,f=0,E=0,y=0,w=!1,_=null;try{if(p=Se(u,r),f=ve,A=Se(m,r),E=ve,0===p||0===A){if(a){if(0!==p)try{a(p,f,1,1)}catch(e){}if(0!==A)try{a(A,E,1,1)}catch(e){}}throw new Error(`WASM malloc failed. Pointers: C=${p}, P=${A}`)}if(y=s(-16),y<=0)throw new Error(`WASM failed to adjust stack pointer correctly (returned ${y}).`);y%8!=0?console.warn(`Result stack pointer ${y} is not 8-byte aligned. Reading f64 might be problematic.`):y%4!=0&&console.warn(`Result stack pointer ${y} is not 4-byte aligned. Reading i32 might be problematic.`),console.log(`Calling wasm_solve with:\n          arg0 (resultStackPtr): ${y}\n          arg1 (challengePtr):   ${p} (len: ${f}) -> "${u}"\n          arg2 (challengeLen):   ${f}\n          arg3 (prefixPtr):      ${A} (len: ${E}) -> "${m}"\n          arg4 (prefixLen):      ${E}\n          arg5 (difficulty):     ${Number(l)}`);const t=performance.now();n(y,p,f,A,E,Number(l));const o=performance.now();console.log(`wasm_solve execution time: ${o-t} ms`);const h=(T=y,me(),T<=0||T+4>ue.byteLength?(console.error(`readInt32FromMemory: Invalid pointer ${T}. Buffer size: ${ue.byteLength}`),0):(T%4!=0&&console.warn(`readInt32FromMemory: Pointer ${T} is not 4-byte aligned.`),ue.getInt32(T,!0)));if(console.log(`WASM solve completed. Read status from stack [${y}]: ${h}`),0===h)throw console.error("WASM solve function indicated failure (status code 0)."),_=null,new Error("WASM solver failed internally (returned status 0).");{const e=function(e){return me(),e<=0||e+8>ue.byteLength?(console.error(`readFloat64FromMemory: Invalid pointer ${e}. Buffer size: ${ue.byteLength}`),NaN):(e%8!=0&&console.warn(`readFloat64FromMemory: Pointer ${e} is not 8-byte aligned.`),ue.getFloat64(e,!0))}(y+8);console.log(`WASM solve success (status ${h}). Read f64 nonce from stack [${y+8}]: ${e}`),_=Math.floor(e)}if(console.log(`Final calculated nonce: ${_}`),s(16),w=!0,null===_||"number"!=typeof _||isNaN(_))throw new Error(`PoW solver did not produce a valid number answer (got: ${_}).`);const v={algorithm:e.algorithm,challenge:i,salt:d,answer:_,signature:c,target_path:g||"/api/v0/chat/completion"},S=JSON.stringify(v);return btoa(S)}catch(e){if(console.error("Error caught during WASM PoW solve:",e),s&&y>0&&!w)try{s(16),console.log("Restored stack pointer in error handler.")}catch(e){console.error("Error restoring stack pointer after error:",e)}throw e}var T}function Ie(e,t,r){return r}class Re extends u{constructor(){super(),this.DEEPSEEK_HOST="https://chat.deepseek.com",this.initialize().catch((e=>{console.error("Failed to initialize DeepseekWebModel:",e)}))}async initialize(){console.log("Initializing DeepSeek model...");try{const e=await m("Deepseek",this.DEEPSEEK_HOST,`${this.DEEPSEEK_HOST}/*`,"deepseekExtractor",!1);e?(this.authToken=`Bearer ${e}`,console.log("[Deepseek Init] Auth token loaded.")):console.warn("[Deepseek Init] Auth token not found during initial load."),await this.initializeStorage(),console.log("DeepSeek model ready.")}catch(t){this.handleModelError("Deepseek Initialization failed",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async ensureAuthToken(){if(!this.authToken){console.log("[Deepseek ensureAuthToken] Auth token missing, attempting to retrieve...");const t=await m("Deepseek",this.DEEPSEEK_HOST,`${this.DEEPSEEK_HOST}/*`,"deepseekExtractor",!0);if(!t)return console.error("[Deepseek ensureAuthToken] Failed to retrieve auth token even after forcing."),this.handleModelError("DeepSeek authentication token is missing. Please log in to https://chat.deepseek.com.",e.UNAUTHORIZED);this.authToken=`Bearer ${t}`}return this.authToken}async initializeStorage(){const e=await this.getAllThreads();e?0===e.length&&await this.saveThreadsToStorage([]):(console.warn("Could not retrieve threads from storage, starting fresh."),await this.saveThreadsToStorage([])),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();if(!e)return;let t=!1;const r=[];for(const a of e)a.modelName===this.getName()?this.isValidDeepseekMetadata(a.metadata)?r.push(a):(console.warn(`Removing Deepseek thread ${a.id} due to invalid metadata.`),t=!0):r.push(a);t&&(await this.saveThreadsToStorage(r),console.log("Removed threads with invalid metadata."))}isValidDeepseekMetadata(e){return"string"==typeof e?.conversationId&&e.conversationId.length>0}getDeepseekMetadata(){const t=this.getCurrentThreadSafe();return t.metadata&&this.isValidDeepseekMetadata(t.metadata)?t.metadata:this.handleModelError("Invalid or missing Deepseek thread metadata.",e.INVALID_METADATA)}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidDeepseekMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log("Loaded existing DeepSeek thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.INVALID_REQUEST)}getName(){return"DeepSeek Web"}supportsImageInput(){return!1}async getHeaders(t=!0){let r={},a="x86",s="Windows",n="64";try{const e=await o.runtime.getPlatformInfo();a=e.arch||a,s="win"===e.os?"Windows":e.os,n="x86-64"===e.arch?"64":"32"}catch(e){}try{const e=navigator.userAgent,t=navigator.language||"en-US";r={Accept:"*/*","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":navigator.languages?navigator.languages.join(","):t,"Cache-Control":"no-cache","Content-Type":"application/json",DNT:"1",Priority:"u=1, i","Sec-CH-UA":void 0!==navigator.userAgentData&&navigator.userAgentData.brands?navigator.userAgentData.brands.map((e=>`"${e.brand}";v="${e.version}"`)).join(", "):'"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',"Sec-CH-UA-Arch":`"${a}"`,"Sec-CH-UA-Bitness":`"${n}"`,"Sec-CH-UA-Full-Version":void 0!==navigator.userAgentData&&navigator.userAgentData.uaFullVersion?`"${navigator.userAgentData.uaFullVersion}"`:'"135.0.7049.116"',"Sec-CH-UA-Full-Version-List":void 0!==navigator.userAgentData&&navigator.userAgentData.brands?navigator.userAgentData.brands.map((e=>`"${e.brand}";v="${e.version}"`)).join(", "):'"Google Chrome";v="135.0.7049.116", "Not-A.Brand";v="*******", "Chromium";v="135.0.7049.116"',"Sec-CH-UA-Mobile":void 0!==navigator.userAgentData&&navigator.userAgentData.mobile?"?1":"?0","Sec-CH-UA-Model":'""',"Sec-CH-UA-Platform":`"${s}"`,"Sec-CH-UA-Platform-Version":'"19.0.0"',"Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","User-Agent":e,"X-App-Version":`${(await this.getPlatformData())[0]}`,"X-Client-Locale":t.replace("-","_"),"X-Client-Platform":"web","X-Client-Version":"1.1.0-new-sse"}}catch(t){this.handleModelError("Failed to get platform info during header creation",e.SERVICE_UNAVAILABLE,void 0,t)}if(t){if(!this.authToken)return this.handleModelError("Authentication token is missing when creating headers.",e.UNAUTHORIZED);r.Authorization=this.authToken}return r}async getPowSolutionForCompletion(t){const r=`${this.DEEPSEEK_HOST}/api/v0/chat/create_pow_challenge`,a={target_path:t||"/api/v0/chat/completion"};let s;try{const t=(await this.makeDeepseekRequest(r,{method:"POST",headers:await this.getHeaders(),body:a,responseType:"json"}))._data;if(0!==t?.code||0!==t?.data?.biz_code||!t?.data?.biz_data?.challenge)return this.handleModelError("Invalid PoW challenge response structure.",e.RESPONSE_PARSING_ERROR,void 0,t);s=t.data.biz_data.challenge}catch(t){return this.handleModelError("Failed to fetch PoW challenge.",e.SERVICE_UNAVAILABLE,void 0,t)}try{return await xe(s)}catch(t){return this.handleModelError("Failed to solve PoW challenge.",e.POW_CHALLENGE_FAILED,void 0,t)}}async makeDeepseekRequest(t,r,a=1){try{if(!r.headers||!r.headers.Authorization){const e=await this.ensureAuthToken();r.headers={...r.headers,Authorization:e}}const e=await this.getHeaders(!1);r.headers={...e,...r.headers},console.debug(`Making Deepseek request: ${r.method||"GET"} ${t}`);const a=await ee.raw(t,r);return console.debug(`Deepseek request to ${t} successful.`),a}catch(r){if(r instanceof q&&r.response){const a=r.response.status,s=r.response.text();try{const t=await s;if(403===a&&(t.includes("cf-challenge-running")||t.includes("Cloudflare")||t.includes("Checking if the site connection is secure")))return this.handleModelError("Cloudflare challenge detected. Please ensure you can access https://chat.deepseek.com in your browser.",e.NETWORK_ERROR,void 0,r)}catch(e){console.warn(`Error reading response body for ${t} after initial failure:`,e)}}return this.handleModelError(`Deepseek API request to ${t} failed`,e.NETWORK_ERROR,void 0,r)}}async getUserInfo(){const t=`${this.DEEPSEEK_HOST}/api/v0/users/current`;try{return(await this.makeDeepseekRequest(t,{method:"GET"}))._data}catch(t){return this.handleModelError("Failed to get user info",e.NETWORK_ERROR,void 0,t)}}async getAllConversationsData(t=100){const r=`${this.DEEPSEEK_HOST}/api/v0/chat_session/fetch_page?count=${t}`;try{const t=await this.makeDeepseekRequest(r,{method:"GET"});if(0===t._data?.code&&0===t._data?.data?.biz_code&&t._data?.data?.biz_data?.chat_sessions)return t._data;{const r=t._data?.msg||t._data?.data?.biz_msg||"Unknown error fetching conversations";return this.handleModelError(`API returned unexpected structure or error: ${r}`,e.RESPONSE_PARSING_ERROR,void 0,t._data)}}catch(t){return this.handleModelError("Failed to get conversations data",e.NETWORK_ERROR,void 0,t)}}async getPlatformData(){let e=null,t=null;const r=`${this.DEEPSEEK_HOST}/version.txt`,a=`${this.DEEPSEEK_HOST}/downloads/status.json`;try{const t=await fetch(r,{method:"GET"});t.ok?e=await t.text():console.warn("Failed to fetch DeepSeek version:",t.status)}catch(e){console.warn("Failed to fetch DeepSeek version:",e)}try{const e=await fetch(a,{method:"GET"});e.ok?t=await e.json():console.warn("Failed to fetch DeepSeek status:",e.status)}catch(e){console.warn("Failed to fetch DeepSeek status:",e)}return[e,t]}async createConversation(){console.log("Crefrerefefr..");const t=`${this.DEEPSEEK_HOST}/api/v0/chat_session/create`;try{const r=(await this.makeDeepseekRequest(t,{method:"POST",body:{character_id:null}}))._data;if(0===r?.code&&0===r.data?.biz_code&&r.data?.biz_data?.id)return console.log(`Created new Deepseek conversation: ${r.data.biz_data.id}`),r.data.biz_data.id;{const t=r?.msg||r?.data?.biz_msg||"Unknown error creating conversation";return this.handleModelError(`Failed to create DeepSeek conversation: ${t}`,e.SERVICE_UNAVAILABLE,void 0,r)}}catch(t){return this.handleModelError("Failed to create conversation",e.SERVICE_UNAVAILABLE,void 0,t)}}async initNewThread(){console.log("ffffffffffffff..");try{await this.ensureAuthToken();const e=await this.createConversation();console.log(`Created new Deepseek conversation: ${e}`),this.currentThread={id:e,title:"New DeepSeek Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:{conversationId:e,lastMessageId:null}},await this.saveThread(),console.log(`Initialized and saved new DeepSeek thread: ${this.currentThread.id} (Conv ID: ${e})`)}catch(t){return this.handleModelError("Failed to initialize new thread",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async doSendMessage(t){let r,a=[];if(t.images&&t.images.length>0){if(t.images.length>4)return this.handleModelError("A maximum of 4 files can be uploaded at once.",e.UPLOAD_AMOUNT_EXCEEDED,t);for(const e of t.images)try{const t=await this.uploadFile(e);a.push(t)}catch(e){return}}try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded(),r=this.getCurrentThreadSafe();const s=this.getDeepseekMetadata(),n=this.createMessage("user",t.prompt);a.length>0&&(n.metadata={...n.metadata||{},uploadedFileIds:a}),r.messages.push(n),r.updatedAt=Date.now(),await this.saveThread();const o=await this.getPowSolutionForCompletion("/api/v0/chat/completion"),i={chat_session_id:s.conversationId,parent_message_id:s.lastMessageId||null,prompt:t.prompt,ref_file_ids:a,thinking_enabled:"reasoning"===t.mode,search_enabled:!0===t.searchEnabled};if(i.search_enabled&&a.length>0)return this.handleModelError("search mode and files attachments can not be used together",e.INVALID_REQUEST,t);const d=`${this.DEEPSEEK_HOST}/api/v0/chat/completion`,l=await this.makeDeepseekRequest(d,{method:"POST",headers:{...await this.getHeaders(),Accept:"*/*","x-ds-pow-response":o},body:i,responseType:"stream",signal:t.signal});if(!l.body)return this.handleModelError("No response body received in stream.",e.NETWORK_ERROR,t);const c=l.body.getReader(),g=new TextDecoder;let h="";const u={text:"",reasoningContent:"",tokensUsed:0,title:void 0,updatedAt:void 0,messageId:void 0,parentId:void 0,model:void 0,role:"ASSISTANT",thinkingEnabled:void 0,banEdit:void 0,banRegenerate:void 0,status:void 0,files:[],tips:[],insertedAt:void 0,searchEnabled:void 0,searchStatus:void 0,searchResults:void 0,reasoningElapsedSecs:void 0};let m=!1,p=null;for(;!m;){const{done:e,value:a}=await c.read();if(e)break;let s;for(h+=g.decode(a,{stream:!0});-1!==(s=h.indexOf("\n\n"));){const e=h.substring(0,s);h=h.substring(s+2);let a=null,n=null;const o=e.split("\n");for(const e of o)if(e.startsWith("event: "))a=e.substring(7).trim();else if(e.startsWith("data: ")){const t=e.substring(5).trim();if(t&&"[DONE]"!==t)try{n=JSON.parse(t)}catch(e){continue}}if("title"===a&&n?.content)u.title=n.content,t.onEvent({type:"TITLE_UPDATE",data:{title:u.title||"",threadId:r.id}});else if("update_session"===a&&n?.updated_at)u.updatedAt=1e3*n.updated_at;else if("close"===a)m=!0;else if("ready"===a);else if(!a&&n?.v&&"object"==typeof n.v&&n.v.response){const e=n.v.response;u.messageId=e.message_id,u.parentId=e.parent_id,u.model=e.model,u.role=e.role,u.thinkingEnabled=e.thinking_enabled,u.banEdit=e.ban_edit,u.banRegenerate=e.ban_regenerate,u.status=e.status,u.files=e.files||[],u.tips=e.tips||[],u.insertedAt=1e3*e.inserted_at,u.searchEnabled=e.search_enabled,u.searchStatus=e.search_status,u.searchResults=e.search_results,u.tokensUsed=e.accumulated_token_usage,u.reasoningContent=e.thinking_content||"",u.reasoningElapsedSecs=e.thinking_elapsed_secs||0}else if(!a&&n?.v&&"string"==typeof n.v){const e=n.p,r=n.v;let a=!1;"response/thinking_content"===e?(p="reasoning",u.reasoningContent=r,a=!0):"response/content"===e?(p="content",u.text=r,a=!0):e||null==r?e&&"response/status"!==e&&console.warn(`Received string data with unhandled path '${e}':`,n):"reasoning"===p?(u.reasoningContent+=r,a=!0):"content"===p?(u.text+=r,a=!0):u.thinkingEnabled?(u.reasoningContent+=r,p="reasoning",a=!0):(u.text+=r,p="content",a=!0),a&&t.onEvent({type:"UPDATE_ANSWER",data:{text:u.text,reasoningContent:u.reasoningContent,reasoningElapsedSecs:u.reasoningElapsedSecs}})}else if(!a&&n?.v&&"number"==typeof n.v){const e=n.p,r=n.v;"response/accumulated_token_usage"===e?u.tokensUsed=r:"response/thinking_elapsed_secs"===e&&(u.reasoningElapsedSecs=r,t.onEvent({type:"UPDATE_ANSWER",data:{text:u.text,reasoningContent:u.reasoningContent,reasoningElapsedSecs:u.reasoningElapsedSecs}}))}else!a&&n?.v&&"string"==typeof n.v&&"response/status"===n.p&&(u.status=n.v,u.status)}}const A=this.createMessage("assistant",u.text);u.reasoningContent&&u.reasoningContent.trim()&&(A.reasoningContent=u.reasoningContent.trim()),A.metadata={...A.metadata||{},responseTokens:u.tokensUsed,serverMessageId:u.messageId,serverParentId:u.parentId,modelUsed:u.model,finalStatus:u.status,reasoningTimeSecs:u.reasoningElapsedSecs,serverInsertedAt:u.insertedAt,thinkingEnabled:u.thinkingEnabled,searchEnabled:u.searchEnabled,searchStatus:u.searchStatus,searchResults:u.searchResults,banEdit:u.banEdit,banRegenerate:u.banRegenerate,files:u.files,tips:u.tips},Object.keys(A.metadata).forEach((e=>{void 0===A.metadata[e]&&delete A.metadata[e]})),r.messages.push(A),A.metadata?.serverMessageId&&(r.metadata||(r.metadata={conversationId:this.getDeepseekMetadata().conversationId}),r.metadata.lastMessageId=A.metadata.serverMessageId),u.title&&(r.title=u.title),u.updatedAt?r.updatedAt=u.updatedAt:r.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:r.id}})}catch(r){this.handleModelError("Error during message sending or processing",r instanceof s?r.code:e.NETWORK_ERROR,t,r)}}async editTitle(t,r){try{let a,s=!1!==r?.loadThread,n=!1!==r?.tryUpdateThread;if(s)await this.ensureThreadLoaded(),a=this.getDeepseekMetadata();else{if(!r?.metadata)return this.handleModelError("No thread loaded and no metadata provided for title edit",e.INVALID_REQUEST);if(a=r.metadata,!this.isValidDeepseekMetadata(a))return this.handleModelError("Invalid metadata provided for title edit",e.INVALID_REQUEST)}const o=a.conversationId;if(!o)return this.handleModelError("Missing chat_session_id for title update",e.INVALID_REQUEST);const i=`${this.DEEPSEEK_HOST}/api/v0/chat_session/update_title`,d={chat_session_id:o,title:t},l=(await this.makeDeepseekRequest(i,{method:"POST",headers:await this.getHeaders(),body:d,responseType:"json"}))._data;if(0!==l?.code||0!==l?.data?.biz_code){const t=l?.msg||l?.data?.biz_msg||"Unknown error updating title";return this.handleModelError(`Failed to update DeepSeek conversation title: ${t}`,e.SERVICE_UNAVAILABLE,void 0,l)}n&&this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError("Error updating DeepSeek conversation title",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{const s=await this.getAllThreads();for(const n of t){const t=s.find((e=>e.metadata&&e.metadata.conversationId===n));if(!t){console.warn(`[deleteServerThreads] Thread ${n} not found locally.`);continue}if(t.modelName!==this.getName()){console.warn(`[deleteServerThreads] Thread ${n} has incorrect model name: ${t.modelName}. Skipping.`);continue}if(!this.isValidDeepseekMetadata(t.metadata)){r?(console.warn(`[deleteServerThreads] Thread ${n} has invalid or missing metadata. Cannot delete from server, deleting locally only.`),await this.deleteThread(t.id,a)):console.warn(`[deleteServerThreads] Thread ${n} has invalid or missing metadata. Cannot delete from server, skipping thread.`);continue}const o=t.metadata.conversationId,i=`${this.DEEPSEEK_HOST}/api/v0/chat_session/delete`,d={chat_session_id:o},l=(await this.makeDeepseekRequest(i,{method:"POST",headers:await this.getHeaders(),body:d,responseType:"json"}))._data;if(0!==l?.code||0!==l?.data?.biz_code){const t=l?.msg||l?.data?.biz_msg||"Unknown error deleting conversation";return this.handleModelError(`Failed to delete DeepSeek conversation: ${t}`,e.SERVICE_UNAVAILABLE,void 0,l)}console.log(`[deleteServerThreads] Successfully deleted DeepSeek conversation: ${o} from server.`),r&&await this.deleteThread(t.id,a)}}catch(t){return this.handleModelError("Error deleting DeepSeek conversation(s)",e.SERVICE_UNAVAILABLE,void 0,t)}}async uploadFile(t){await this.ensureAuthToken();const r=await this.getPowSolutionForCompletion("/api/v0/file/upload_file"),a=`${this.DEEPSEEK_HOST}/api/v0/file/upload_file`,s=new FormData;s.append("file",t);const n={};let o;this.authToken&&(n.Authorization=this.authToken),n["x-ds-pow-response"]=r;try{const e=await fetch(a,{method:"POST",headers:n,body:s,credentials:"include"});o=await e.json()}catch(t){return this.handleModelError("Failed to upload file to Deepseek",e.UPLOAD_FAILED,void 0,t)}if(0!==o.code||0!==o.data?.biz_code){const t=o.data?.biz_msg||o.msg||"Unknown error uploading file";return this.handleModelError(`Deepseek file upload failed: ${t}`,e.UPLOAD_FAILED,void 0,o)}let i=o.data?.biz_data;return i&&i.id?("PENDING"!==i.status&&"PARSING"!==i.status||(i=await this.pollFileStatus(i.id)),"CONTENT_EMPTY"===i.status?this.handleModelError("No text could be extracted from the image.",e.UPLOAD_FAILED,void 0,i):"UNSUPPORTED"===i.status?this.handleModelError("File type not supported for Deepseek",e.UPLOAD_FAILED,void 0,i):"SUCCESS"!==i.status?this.handleModelError(`File upload failed or not supported (status: ${i.status})`,e.UPLOAD_FAILED,void 0,i):i.id):this.handleModelError("Deepseek file upload: missing file id in response",e.UPLOAD_FAILED,void 0,o)}async pollFileStatus(t,r=10,a=1500){const s=`${this.DEEPSEEK_HOST}/api/v0/file/fetch_files?file_ids=${encodeURIComponent(t)}`;for(let e=0;e<r;++e){let e;await new Promise((e=>setTimeout(e,a)));try{const t=await fetch(s,{method:"GET",credentials:"include",headers:this.authToken?{Authorization:this.authToken}:{}});e=await t.json()}catch(e){continue}if(0===e.code&&0===e.data?.biz_code&&e.data?.biz_data?.files?.[0]){const t=e.data.biz_data.files[0];if(["SUCCESS","CONTENT_EMPTY","FAILED","UNSUPPORTED"].includes(t.status))return t}}return this.handleModelError("File processing timed out on Deepseek",e.UPLOAD_FAILED)}}p([Ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],Re.prototype,"getUserInfo",null),p([Ie,A("design:type",Function),A("design:paramtypes",[Number]),A("design:returntype",Promise)],Re.prototype,"getAllConversationsData",null),p([Ie,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],Re.prototype,"getPlatformData",null),p([Ie,A("design:type",Function),A("design:paramtypes",[String,Object]),A("design:returntype",Promise)],Re.prototype,"editTitle",null),p([Ie,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean]),A("design:returntype",Promise)],Re.prototype,"deleteServerThreads",null),console.log("Background script loaded.");const Ne={deepseekExtractor:function(){try{const e=localStorage.getItem("userToken");if(e){const t=JSON.parse(e);if(t?.value&&"string"==typeof t.value&&t.value.length>0)return t.value;console.warn("Deepseek 'userToken' found but userToken structure not matched:",t)}else console.warn("Deepseek 'userToken' key not found in localStorage.")}catch(e){console.error("Error executing injected DeepSeek token extractor:",e)}return null},copilotExtractor:function(){try{for(let e=0;e<localStorage.length;e++)try{const t=localStorage.key(e);if(!t)continue;const r=JSON.parse(localStorage.getItem(t)||"");if(r&&"AccessToken"===r.credentialType&&r.expiresOn>Math.floor(Date.now()/1e3)&&r.target?.includes("ChatAI"))return r.secret}catch(e){}}catch(e){console.error("Error executing injected Copilot token extractor:",e)}return null}};a().runtime.onMessage.addListener(((e,t,r)=>{if(console.log("[Background] Received message:",e?.type),"GET_AUTH_TOKEN_FROM_WEBSITE"===e.type){const{serviceName:t,targetUrl:a,urlPattern:s,extractorName:n,forceNewTab:i}=e.payload||{};if(!(t&&a&&s&&n))return console.error("[Background] Invalid payload received for GET_AUTH_TOKEN_FROM_WEBSITE:",e.payload),r({success:!1,error:"Invalid payload received by background script."}),!1;const d=Ne[n];return d?(console.log(`[Background] Processing token request for service: ${t} using extractor: ${n}`),async function(e,t,r,a,s){let n=null;if(console.log(`[${e} Auth Internal] Attempting to retrieve token...`),void 0===o||!o.tabs||!o.scripting||!o.permissions)return console.error(`[${e} Auth Internal] Browser extension APIs (tabs, scripting, permissions) not available.`),null;let i=!1;try{i=await o.permissions.contains({origins:[r]})}catch(t){return console.error(`[${e} Auth Internal] Error checking permissions for ${r}:`,t),null}if(!i)return console.warn(`[${e} Auth Internal] Missing host permissions for ${r}. Cannot inject scripts or open tabs.`),null;if(s)console.log(`[${e} Auth Internal] Skipping existing tab check because forceNewTab is true.`);else try{const s=await o.tabs.query({url:r});console.log(`[${e} Auth Internal] Found ${s.length} potential tabs matching ${r}.`);for(const r of s)if(!r.id||r.url?.startsWith("chrome://")||r.url?.startsWith("about:"))console.log(`[${e} Auth Internal] Skipping tab ID: ${r.id} (missing ID or restricted URL: ${r.url})`);else{console.log(`[${e} Auth Internal] Attempting to inject script into tab ID: ${r.id} (URL: ${r.url})`);try{const t=await o.scripting.executeScript({target:{tabId:r.id},func:a,world:"MAIN"});if(t&&t[0]&&t[0].result){n=t[0].result,console.log(`[${e} Auth Internal] Successfully extracted token from existing tab: ${r.id}`);break}console.log(`[${e} Auth Internal] Script executed on tab ${r.id}, but no token found in result.`,t)}catch(t){t instanceof Error&&t.message.includes("No window matching")?console.warn(`[${e} Auth Internal] Could not inject into tab ${r.id} (window likely closed).`):t instanceof Error&&t.message.includes("Could not establish connection")?console.warn(`[${e} Auth Internal] Connection error injecting into tab ${r.id} (possibly devtools?).`):t instanceof Error&&t.message.includes("Cannot access contents of the page")?console.warn(`[${e} Auth Internal] Cannot access contents of tab ${r.id} (URL: ${r.url}). Might be restricted page.`):console.warn(`[${e} Auth Internal] Failed to inject script or extract token from tab ${r.id}:`,t)}}}catch(t){console.error(`[${e} Auth Internal] Error querying for tabs matching ${r}:`,t)}if(!n){if(console.log(`[${e} Auth Internal] No token from existing tabs. Proceeding with temporary window method...`),!o.windows)return console.error(`[${e} Auth Internal] Browser.windows API is not available. Cannot open temporary window.`),null;let r=null;try{console.log(`[${e} Auth Internal] Creating temporary window for ${t}...`),r=await o.windows.create({url:t,focused:!1,type:"popup",width:150,height:150}),console.log(`[${e} Auth Internal] Temporary window created (ID: ${r?.id})`);const s=r?.tabs?.[0]?.id;if(!s)throw new Error("Failed to get tab ID from temporary window.");console.log(`[${e} Auth Internal] Temporary tab ID: ${s}`),console.log(`[${e} Auth Internal] Waiting for temporary tab to load...`),await new Promise((e=>setTimeout(e,4e3))),console.log(`[${e} Auth Internal] Attempting REAL extractor injection into temp tab ID: ${s}`);try{const t=await o.scripting.executeScript({target:{tabId:s},func:a,world:"MAIN"});console.log(`[${e} Auth Internal] REAL extractor script executed. Results:`,t),t&&t[0]&&t[0].result?(n=t[0].result,console.log(`[${e} Auth Internal] Successfully extracted REAL token via temporary tab.`)):console.warn(`[${e} Auth Internal] Failed to extract REAL token via temporary tab.`,t)}catch(t){console.error(`[${e} Auth Internal] Error injecting REAL extractor script:`,t)}}catch(t){console.error(`[${e} Auth Internal] Error creating/accessing temporary tab:`,t)}finally{if(console.log(`[${e} Auth Internal] Entering finally block for temporary window.`),r?.id){console.log(`[${e} Auth Internal] Attempting to close temporary window ID: ${r.id}`);try{await o.windows.remove(r.id),console.log(`[${e} Auth Internal] Successfully closed temporary auth window.`)}catch(t){t instanceof Error&&t.message.includes("No window with id")?console.log(`[${e} Auth Internal] Temporary window already closed.`):console.warn(`[${e} Auth Internal] Error closing temporary auth window:`,t)}}else console.log(`[${e} Auth Internal] No temporary window ID found to close in finally block.`)}}return n?console.log(`[${e} Auth Internal] Token retrieval successful.`):console.warn(`[${e} Auth Internal] Failed to retrieve token after all attempts.`),n}(t,a,s,d,i??!1).then((e=>{console.log(`[Background ${t}] Token result:`,e?"Token Found":"Not Found"),r({success:!0,token:e||null})})).catch((e=>{console.error(`[Background ${t}] Error retrieving token:`,e),r({success:!1,error:e instanceof Error?e.message:String(e)})})),!0):(console.error(`[Background] Unknown extractor function name received: ${n}`),r({success:!1,error:`Unknown extractor function: ${n}`}),!1)}return"PING"===e.type&&(console.log("[Background] Received PING from",t?.tab?.id||t?.id),r({success:!0,message:"PONG"}),!1)})),console.log("Background script message listener attached.")})()})();